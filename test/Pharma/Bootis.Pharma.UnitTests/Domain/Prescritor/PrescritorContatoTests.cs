using Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate;
using Bootis.Pessoa.UnitTests.Fixtures.PrescritorContato;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;

namespace Bootis.Pessoa.UnitTests.Domain.Prescritor;

public class PrescritorContatoTests : BaseTest
{
    private readonly PrescritorContato PrescritorContato = PrescritorContatoFakes.CriarCommandValido();

    [Fact]
    public void PrescritorContato_DeveAtualizarPrescritorContato()
    {
        //Arrange 
        var cmd = AtualizarRequestPrescritorContatoFakes.CriarCommandValido();

        //Action
        PrescritorContato.Atualizar(1.ToGuid(), cmd.TipoContatoId, cmd.Identificacao, cmd.Principal, cmd.Observacao);

        //Assert
        Assert.Equal(cmd.TipoContatoId, PrescritorContato.TipoContatoId);
        Assert.Equal(cmd.Identificacao, PrescritorContato.Identificacao);
        Assert.Equal(cmd.Principal, PrescritorContato.Principal);
    }

    [Theory]
    [InlineData(false)]
    [InlineData(true)]
    public void PrescritorContato_DeveAtualizarPrincipal(bool principal)
    {
        //Action
        PrescritorContato.AtualizarPrincipal(principal);

        //Assert
        Assert.Equal(principal, PrescritorContato.Principal);
    }
}
