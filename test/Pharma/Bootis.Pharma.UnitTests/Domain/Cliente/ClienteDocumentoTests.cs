using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;

namespace Bootis.Pessoa.UnitTests.Domain.Cliente;

public class ClienteDocumentoTests : BaseTest
{
    private readonly ClienteDocumento ClienteDocumento =
        ClienteDocumentoFake.CriarCommandValido();

    [Fact]
    public void ClienteDocumento_DeveAtualizarClienteDocumento()
    {
        //Arrange 
        var cmd = ClienteDocumentoFake.CriarCommandValido();

        //Action
        ClienteDocumento.Atualizar(1.ToGuid(), cmd.Identificacao, cmd.TipoDocumentoId, cmd.Observacao);

        //Assert
        Assert.Equal(cmd.Identificacao, ClienteDocumento.Identificacao);
        Assert.Equal(cmd.TipoDocumentoId, ClienteDocumento.TipoDocumentoId);
    }
}
