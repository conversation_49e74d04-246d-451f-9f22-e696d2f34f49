using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;

namespace Bootis.Estoque.UnitTests.Domain;

public class InventarioEspecificacaoTests : BaseTest
{
    private readonly Inventario _inventario = InventarioFake.CriarInventarioValido();

    [Fact]
    public void InventarioItem_DeveLancarQuantidadeInventariada()
    {
        //Arrange 
        var localEstoqueId = 1.ToGuid();
        var grupoId = 2.ToGuid();
        var subGrupoId = 3.ToGuid();

        //Action
        _inventario.InventarioEspecificacao.First()
            .AtualizarInventarioEspecificacao(localEstoqueId, grupoId, subGrupoId);

        //Assert
        Assert.Equal(localEstoqueId, _inventario.InventarioEspecificacao.First().LocalEstoqueId);
        Assert.Equal(grupoId, _inventario.InventarioEspecificacao.First().GrupoId);
        Assert.Equal(subGrupoId, _inventario.InventarioEspecificacao.First().SubGrupoId);
    }
}
