using Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate;
using Bootis.Compra.UnitTests.Fixtures;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;

namespace Bootis.Compra.UnitTests.Domain.NotaFiscalEntrada;

public class NotaFiscalEntradaLoteInformacaoTecnicaTests : BaseTest
{
    private readonly NotaFiscalEntradaLote NotaFiscalEntradaLote = new();

    [Fact]
    public void NotaFiscalEntradaLote_DeveAtualizarNotaFiscalEntradaLoteInformacaoTecnica()
    {
        //Arrange
        var localEstoqueId = 1.ToGuid();
        var paisId = 1.ToGuid();
        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();
        var cmd = CadastrarNotaFiscalEntradaLoteCommandFake
            .CriarCommandValidoInformacaoTecnicaELoteUnidadeAlternativa_Atualizar().Lotes.First();
        var cmdInfoTecnica = cmd.InformacaoTecnica;
        NotaFiscalEntradaLote.AtualizarNotaFiscalEntradaLote(cmd, localEstoqueId, produto, paisId);

        //Action
        NotaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica.AtualizarLoteInformacaoTecnica(paisId,
            cmdInfoTecnica.DiluicaoFornecedor, cmdInfoTecnica.FatorDiluicaoFornecedor,
            cmdInfoTecnica.ConcentracaoAgua, cmdInfoTecnica.FatorConcentracaoAgua, cmdInfoTecnica.Densidade);

        //Assert
        Assert.Equal(cmd.InformacaoTecnica.ConcentracaoAgua,
            NotaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica.ConcentracaoAgua);
    }

    [Fact(Skip = "Need to be fixed")]
    public void NotaFiscalEntradaLote_DeveAtualizarNotaFiscalEntradaLote_InformacaoTecnica()
    {
        //Arrange
        var localEstoqueId = 1.ToGuid();
        var paisId = 1.ToGuid();
        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();
        var cmd = CadastrarNotaFiscalEntradaLoteCommandFake
            .CriarCommandValidoInformacaoTecnicaELoteUnidadeAlternativa_Atualizar().Lotes.First();
        var cmdInfoTecnica = cmd.InformacaoTecnica;
        NotaFiscalEntradaLote.AtualizarNotaFiscalEntradaLote(cmd, localEstoqueId, produto, paisId);

        //Action
        NotaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica.AtualizarUnidadesAlternativa(
            cmdInfoTecnica.LoteUnidadeAlternativa1, cmdInfoTecnica.LoteUnidadeAlternativa2);

        //Assert
        Assert.Equal(cmd.InformacaoTecnica.LoteUnidadeAlternativa1.QuantidadeUnidadeAlternativa,
            NotaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica.NotaFiscalEntradaLoteUnidadeAlternativa.First()
                .QuantidadeUnidadeAlternativa);
        Assert.Equal(cmd.InformacaoTecnica.LoteUnidadeAlternativa2.QuantidadeUnidadeAlternativa,
            NotaFiscalEntradaLote.NotaFiscalEntradaLoteInformacaoTecnica.NotaFiscalEntradaLoteUnidadeAlternativa.Last()
                .QuantidadeUnidadeAlternativa);
    }
}
