using Bootis.Catalogo.Application.UseCases.ProdutoSinonimo;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate;
using Bootis.Estoque.UnitTests.Fixtures.ProdutoSinonimo;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Estoque.UnitTests.Application.ProdutoSinonimo;

public class AtualizarProdutoSinonimoCommandHandlerTests : BaseTest
{
    private readonly AtualizarRequestHandler _atualizarProdutoSinonimoCommandHandler;
    private readonly Mock<IProdutoSinonimoRepository> _produtoSinonimoRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarProdutoSinonimoCommandHandlerTests()
    {
        _produtoSinonimoRepositoryMock = new Mock<IProdutoSinonimoRepository>();

        unitOfWork.Setup(l => l.GetRepository<IProdutoSinonimoRepository>())
            .Returns(_produtoSinonimoRepositoryMock.Object);

        _atualizarProdutoSinonimoCommandHandler =
            new AtualizarRequestHandler(unitOfWork.Object);
    }

    [Fact]
    public async Task AtualizarProdutoSinonimo_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = AtualizarProdutoSinonimoCommandFake.CreateAtualizarProdutoSinonimoCommandValido();
        var model = ProdutoSinonimoFake.CreateProdutoSinonimoValido();

        _produtoSinonimoRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(model));

        _produtoSinonimoRepositoryMock.Setup(l => l.ValidarPorIdEDescricaoAsync(model.Id, cmd.Sinonimo))
            .Returns(
                Task.FromResult(false));

        _produtoSinonimoRepositoryMock.Setup(l => l.ValidarPorDescricaoAsync(cmd.Sinonimo)).Returns(
            Task.FromResult(false));

        _produtoSinonimoRepositoryMock.Setup(l => l.Update(model))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _atualizarProdutoSinonimoCommandHandler.Handle(cmd, default);

        //Assert
        _produtoSinonimoRepositoryMock.Verify(
            l => l.Update(
                It.Is<Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate.ProdutoSinonimo>(m =>
                    m.DescricaoRotulo == model.DescricaoRotulo)),
            Times.Once);
    }

    [Fact]
    public async Task AtualizarProdutoSinonimo_ExecutadoComErro_GuidNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarProdutoSinonimoCommandFake.CreateAtualizarProdutoSinonimoCommandValido();
        var model = ProdutoSinonimoFake.CreateProdutoSinonimoValido();

        _produtoSinonimoRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult<Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate.ProdutoSinonimo>(default));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _atualizarProdutoSinonimoCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _produtoSinonimoRepositoryMock.Verify(
            l => l.Update(
                It.Is<Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate.ProdutoSinonimo>(m =>
                    m.DescricaoRotulo == model.DescricaoRotulo)),
            Times.Never);
    }

    [Fact]
    public async Task AtualizarProdutoSinonimo_ExecutadoComErro_DescricaoExistente()
    {
        //Arrange
        var cmd = AtualizarProdutoSinonimoCommandFake.CreateAtualizarProdutoSinonimoCommandValido();
        var model = ProdutoSinonimoFake.CreateProdutoSinonimoValido();
        var produtoDescricao = new Mock<Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate.ProdutoSinonimo>(
            ProdutoFake.CreateProdutoMateriaPrimaFake(),
            model.Sinonimo, model.DescricaoRotulo, model.FatorEquivalencia, model.PercentualCorrecao);

        produtoDescricao.SetupGet(l => l.Id).Returns(1.ToGuid());

        _produtoSinonimoRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(model));

        _produtoSinonimoRepositoryMock.Setup(l => l.ValidarPorIdEDescricaoAsync(model.Id, cmd.Sinonimo))
            .Returns(
                Task.FromResult(false));

        _produtoSinonimoRepositoryMock.Setup(l => l.ValidarPorDescricaoAsync(cmd.Sinonimo)).Returns(
            Task.FromResult(true));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _atualizarProdutoSinonimoCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _produtoSinonimoRepositoryMock.Verify(
            l => l.Update(
                It.Is<Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate.ProdutoSinonimo>(m =>
                    m.DescricaoRotulo == model.DescricaoRotulo)),
            Times.Never);
    }

    [Fact]
    public async Task AtualizarProdutoSinonimo_ExecutadoComErro_SinonimoIgualAhDescricaoDoProduto()
    {
        //Arrange
        var cmd =
            AtualizarProdutoSinonimoCommandFake.CreateAtualizarProdutoSinonimoCommandValido_SinonimoIgualAoProduto();
        var model = ProdutoSinonimoFake.CreateProdutoSinonimoValido();

        _produtoSinonimoRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(model));

        _produtoSinonimoRepositoryMock.Setup(l => l.ValidarPorIdEDescricaoAsync(It.IsAny<Guid>(), cmd.Sinonimo))
            .Returns(
                Task.FromResult(false));

        _produtoSinonimoRepositoryMock.Setup(l => l.ValidarPorDescricaoAsync(cmd.Sinonimo)).Returns(
            Task.FromResult(false));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _atualizarProdutoSinonimoCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _produtoSinonimoRepositoryMock.Verify(
            l => l.Update(
                It.Is<Catalogo.Domain.AggregatesModel.ProdutoSinonimoAggregate.ProdutoSinonimo>(m =>
                    m.DescricaoRotulo == model.DescricaoRotulo)),
            Times.Never);
    }
}
