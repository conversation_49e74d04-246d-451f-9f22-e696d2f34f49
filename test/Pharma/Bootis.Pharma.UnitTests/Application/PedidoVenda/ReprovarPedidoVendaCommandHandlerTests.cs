using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;
using Bootis.Venda.Application.Extensions;
using Bootis.Venda.Application.UseCases.PedidoVenda;
using Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate;
using Bootis.Venda.Domain.Enumerations;
using Bootis.Venda.UnitTests.Fixtures.PedidoVenda;
using Moq;

namespace Bootis.Venda.UnitTests.Application.PedidoVenda;

public class ReprovarPedidoVendaCommandHandlerTests : BaseTest
{
    private readonly ReprovarRequestHandler _handler;
    private readonly Mock<IPedidoVendaRepository> _pedidoVendaRepositoryMock;
    private readonly Mock<IUsuarioRepository> _usuarioRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public ReprovarPedidoVendaCommandHandlerTests()
    {
        _pedidoVendaRepositoryMock = new Mock<IPedidoVendaRepository>();
        _usuarioRepositoryMock = new Mock<IUsuarioRepository>();
        _handler = new ReprovarRequestHandler(unitOfWork.Object, UserContext.Object, _pedidoVendaRepositoryMock.Object,
            _usuarioRepositoryMock.Object);
    }

    [Fact]
    public async Task ReprovarPedidoVenda_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = ReprovarPedidoVendaCommandFake.CriarCommandValido();

        var itens = new List<PedidoVendaItemProdutoAcabado>
        {
            new() { Status = StatusVendaItem.Orcado },
            new() { Status = StatusVendaItem.Reprovado }
        };

        var pedidoVenda = PedidoVendaFake.CreatePedidoVendaComItensProdutoAcabado(itens);

        _pedidoVendaRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(pedidoVenda);

        _pedidoVendaRepositoryMock
            .Setup(l => l.Update(It.IsAny<Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda>()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _pedidoVendaRepositoryMock
            .Verify(
                l => l.Update(
                    It.Is<Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda>(m =>
                        m.Status == StatusVenda.Reprovado)), Times.Once);

        Assert.All(pedidoVenda.Itens, item => Assert.Equal(StatusVendaItem.Reprovado, item.Status));
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task ReprovarPedidoVenda_ExecutadoComErro_PedidoVendaNaoEncontrado()
    {
        //Arrange
        var cmd = ReprovarPedidoVendaCommandFake.CriarCommandValido();
        var pedidoVenda = PedidoVendaFake.CreatePedidoVendaComItensProdutoAcabado();

        _pedidoVendaRepositoryMock.Setup(l => l.ObterPedidoVendaAsync(It.IsAny<Guid>()))
            .ThrowsAsync(new ValidationException("PedidoVenda n�o encontrado"));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _pedidoVendaRepositoryMock.Verify(
            l => l.Update(
                It.Is<Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda>(m =>
                    m.Observacao == pedidoVenda.Observacao)),
            Times.Never);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task ReprovarPedidoVenda_ExecutadoComErro_StatusInvalido()
    {
        //Arrange
        var cmd = ReprovarPedidoVendaCommandFake.CriarCommandValido();
        var pedidoVenda = PedidoVendaFake.CreatePedidoVenda();

        pedidoVenda.AtualizarStatus(StatusVenda.Aprovado, 1.ToGuid(), false);

        _pedidoVendaRepositoryMock.Setup(l => l.ObterPedidoVendaAsync(cmd.PedidoVendaId))
            .Returns(Task.FromResult(pedidoVenda));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<DomainException>(executionResult);
        _pedidoVendaRepositoryMock
            .Verify(l => l.Update(It.IsAny<Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda>()), Times.Never);
    }

    [Fact(Skip = "Need to be fixed")]
    public async Task ReprovarPedidoVenda_ExecutadoComErro_UsuarioNaoEncontrado()
    {
        //Arrange
        var cmd = ReprovarPedidoVendaCommandFake.CriarCommandValido();
        var usuario = UsuarioFakes.GerarUsuarioValido();
        var pedidoVenda = PedidoVendaFake.CreatePedidoVenda();

        pedidoVenda.AtualizarStatus(StatusVenda.Orcamento, usuario.Id, false);

        _pedidoVendaRepositoryMock.Setup(l => l.ObterPedidoVendaAsync(cmd.PedidoVendaId))
            .Returns(Task.FromResult(pedidoVenda));

        _usuarioRepositoryMock.Setup(l => l.GetByIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync((Usuario)null);

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _pedidoVendaRepositoryMock
            .Verify(
                l => l.Update(
                    It.Is<Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda>(m =>
                        m.Status == StatusVenda.Orcamento)), Times.Never);
    }
}
