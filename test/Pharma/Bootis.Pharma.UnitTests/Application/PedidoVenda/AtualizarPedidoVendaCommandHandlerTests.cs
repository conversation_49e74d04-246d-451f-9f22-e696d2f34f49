using Bootis.Compra.Domain.Enumerations;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Bootis.Venda.Application.UseCases.PedidoVenda;
using Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate;
using Bootis.Venda.UnitTests.Fixtures.PedidoVenda;
using Moq;

namespace Bootis.Venda.UnitTests.Application.PedidoVenda;

public class AtualizarPedidoVendaCommandHandlerTests : BaseTest
{
    private readonly AtualizarPedidoVendaRequestHandler _handler;
    private readonly Mock<IPedidoVendaRepository> _pedidoVendaRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarPedidoVendaCommandHandlerTests()
    {
        _pedidoVendaRepositoryMock = new Mock<IPedidoVendaRepository>();
        _handler = new AtualizarPedidoVendaRequestHandler(unitOfWork.Object, _pedidoVendaRepositoryMock.Object);
    }

    [Fact]
    public async Task AtualizarPedidoVenda_ExecutadoComSucesso()
    {
        // Arrange
        var cmd = AtualizarPedidoVendaCommandFake.CriarCommandValido();
        var item = PedidoVendaItemProdutoAcabadoFake.CreatePedidoVendaItemProdutoAcabado();
        var pedidoVenda = PedidoVendaFake.CreatePedidoVendaComItemProdutoAcabado((PedidoVendaItemProdutoAcabado)item);

        _pedidoVendaRepositoryMock.Setup(l => l.GetByIdAsync(cmd.PedidoVendaId))
            .Returns(Task.FromResult(pedidoVenda));

        _pedidoVendaRepositoryMock.Setup(l => l.Update(pedidoVenda))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        // Action
        await _handler.Handle(cmd, default);

        // Assert
        Assert.Equal(2, pedidoVenda.Itens.SingleOrDefault().ValorDescontoRateio);
        Assert.Equal(2, pedidoVenda.ValorDescontoRateio);
        Assert.Equal(6, pedidoVenda.ValorDescontoTotal);
        Assert.Equal(14, pedidoVenda.Totalizador.TotalPedido);
    }

    [Fact]
    public async Task AtualizarPedidoVenda_ExecutadoComSucesso_AtualizaTipo()
    {
        // Arrange
        var cmd = AtualizarPedidoVendaCommandFake.CriarCommandValido_AtualizaTipo();
        var item = PedidoVendaItemProdutoAcabadoFake.CreatePedidoVendaItemProdutoAcabado();
        var pedidoVenda = PedidoVendaFake.CreatePedidoVendaComItemProdutoAcabado((PedidoVendaItemProdutoAcabado)item);

        _pedidoVendaRepositoryMock.Setup(l => l.GetByIdAsync(cmd.PedidoVendaId))
            .Returns(Task.FromResult(pedidoVenda));

        _pedidoVendaRepositoryMock.Setup(l => l.Update(pedidoVenda))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        // Action
        await _handler.Handle(cmd, default);

        // Assert
        Assert.Equal(TipoDesconto.DescontoPorcentagem, pedidoVenda.TipoDesconto);
    }

    [Fact]
    public async Task AtualizarPedidoVenda_ExecutadoComSucesso_AtualizaValorDesconto()
    {
        // Arrange
        var cmd = AtualizarPedidoVendaCommandFake.CriarCommandValido_AtualizaValorDesconto();
        var item = PedidoVendaItemProdutoAcabadoFake.CreatePedidoVendaItemProdutoAcabado();
        var pedidoVenda = PedidoVendaFake.CreatePedidoVendaComItemProdutoAcabado((PedidoVendaItemProdutoAcabado)item);

        _pedidoVendaRepositoryMock.Setup(l => l.GetByIdAsync(cmd.PedidoVendaId))
            .Returns(Task.FromResult(pedidoVenda));

        _pedidoVendaRepositoryMock.Setup(l => l.Update(pedidoVenda))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        // Action
        await _handler.Handle(cmd, default);

        // Assert
        Assert.Equal(TipoDesconto.DescontoMonetario, pedidoVenda.TipoDesconto);
        Assert.Equal(4, pedidoVenda.Desconto);
    }

    [Fact]
    public async Task AtualizarPedidoVenda_ExecutadoComSucesso_AtualizaValorTotal()
    {
        // Arrange
        var cmd = AtualizarPedidoVendaCommandFake.CriarCommandValido_AtualizaValorTotal();
        var item = PedidoVendaItemProdutoAcabadoFake.CreatePedidoVendaItemProdutoAcabado();
        var pedidoVenda = PedidoVendaFake.CreatePedidoVendaComItemProdutoAcabado((PedidoVendaItemProdutoAcabado)item);

        _pedidoVendaRepositoryMock.Setup(l => l.GetByIdAsync(cmd.PedidoVendaId))
            .Returns(Task.FromResult(pedidoVenda));

        _pedidoVendaRepositoryMock.Setup(l => l.Update(pedidoVenda))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        // Action
        await _handler.Handle(cmd, default);

        // Assert
        Assert.Equal(5, pedidoVenda.Itens.SingleOrDefault().ValorDescontoRateio);
    }

    [Fact]
    public async Task AtualizarPedidoVenda_ExecutadoComErro_SomaDescontoInvalido()
    {
        // Arrange
        var cmd = AtualizarPedidoVendaCommandFake.CriarCommandSomaDescontoInvalido();
        var item = PedidoVendaItemProdutoAcabadoFake.CreatePedidoVendaItemProdutoAcabado();
        var pedidoVenda = PedidoVendaFake.CreatePedidoVendaComItemProdutoAcabado((PedidoVendaItemProdutoAcabado)item);

        _pedidoVendaRepositoryMock.Setup(l => l.GetByIdAsync(cmd.PedidoVendaId))
            .Returns(Task.FromResult(pedidoVenda));

        _pedidoVendaRepositoryMock.Setup(l => l.Update(pedidoVenda))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        // Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        // Assert
        Assert.IsType<DomainException>(executionResult);
        _pedidoVendaRepositoryMock
            .Verify(
                l => l.Update(
                    It.Is<Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda>(m =>
                        m.Observacao == pedidoVenda.Observacao)), Times.Never);
    }

    [Fact]
    public async Task AtualizarPedidoVenda_ExecutadoComErro_ValorDescontoInvalido()
    {
        // Arrange
        var cmd = AtualizarPedidoVendaCommandFake.CriarCommandValorDescontoInvalido();
        var item = PedidoVendaItemProdutoAcabadoFake.CreatePedidoVendaItemProdutoAcabado();
        var pedidoVenda = PedidoVendaFake.CreatePedidoVendaComItemProdutoAcabado((PedidoVendaItemProdutoAcabado)item);

        _pedidoVendaRepositoryMock.Setup(l => l.GetByIdAsync(cmd.PedidoVendaId))
            .Returns(Task.FromResult(pedidoVenda));

        _pedidoVendaRepositoryMock.Setup(l => l.Update(pedidoVenda))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        // Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        // Assert
        Assert.IsType<DomainException>(executionResult);
        _pedidoVendaRepositoryMock
            .Verify(
                l => l.Update(
                    It.Is<Domain.AggregatesModel.PedidoVendaAggregate.PedidoVenda>(m =>
                        m.Observacao == pedidoVenda.Observacao)), Times.Never);
    }
}
