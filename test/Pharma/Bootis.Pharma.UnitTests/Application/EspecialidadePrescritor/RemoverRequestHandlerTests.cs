using Bootis.Pessoa.Application.Requests.EspecialidadePrescritor.Remover;
using Bootis.Pessoa.Application.UseCases.EspecialidadePrescritor;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;
using EspecialidadePrescritorModel = Bootis.Pessoa.Domain.AggregatesModel.EspecialidadePrescritorAggregate;

namespace Bootis.Pessoa.UnitTests.Application.EspecialidadePrescritor;

public class RemoverRequestHandlerTests : BaseTest
{
    private readonly Mock<EspecialidadePrescritorModel.IEspecialidadePrescritorRepository>
        _especialidadePrescritorRepositoryMock;

    private readonly RemoverRequestHandler _handler;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public RemoverRequestHandlerTests()
    {
        _especialidadePrescritorRepositoryMock =
            new Mock<EspecialidadePrescritorModel.IEspecialidadePrescritorRepository>();
        _handler = new RemoverRequestHandler(unitOfWork.Object, _especialidadePrescritorRepositoryMock.Object);
    }

    [Fact]
    public async Task RemoverEspecialidadePrescritor_ExecutadoComSucesso()
    {
        //Arrange
        var model = new List<EspecialidadePrescritorModel.EspecialidadePrescritor>
        {
            EspecialidadePrescritorFakes.CriarEspecialidadePrescritorValido()
        };
        var cmd = new RemoverRequest(new List<Guid> { model.First().Id });

        _especialidadePrescritorRepositoryMock.Setup(l => l.ObterPorIdsAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(model);

        _especialidadePrescritorRepositoryMock.Setup(l => l.Remove(model.First()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .ReturnsAsync(1);

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _especialidadePrescritorRepositoryMock
            .Verify(
                l => l.Remove(
                    It.Is<EspecialidadePrescritorModel.EspecialidadePrescritor>(m =>
                        m.Descricao == model.First().Descricao)), Times.Once);
    }

    [Fact]
    public async Task RemoverEspecialidadePrescritor_ExecutadoComErro_EspecialidadePrescritorGuidNaoEncontrado()
    {
        //Arrange
        var model = EspecialidadePrescritorFakes.CriarEspecialidadePrescritorValido();
        var especialidades = new List<EspecialidadePrescritorModel.EspecialidadePrescritor>();
        var cmd = new RemoverRequest(new List<Guid> { model.Id });

        _especialidadePrescritorRepositoryMock.Setup(l => l.ObterPorIdsAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(especialidades);

        //Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(exception);
        _especialidadePrescritorRepositoryMock
            .Verify(l => l.Remove(It.IsAny<EspecialidadePrescritorModel.EspecialidadePrescritor>()), Times.Never);
        unitOfWork
            .Verify(l => l.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task RemoverEspecialidadePrescritor_ExecutadoComErro_ComDependencias()
    {
        //Arrange
        var model = new List<EspecialidadePrescritorModel.EspecialidadePrescritor>
        {
            EspecialidadePrescritorFakes.CriarEspecialidadePrescritorValido()
        };
        var cmd = new RemoverRequest(new List<Guid> { model.First().Id });

        _especialidadePrescritorRepositoryMock.Setup(l => l.ObterPorIdsAsync(It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(model);

        _especialidadePrescritorRepositoryMock
            .Setup(l => l.VerificaDependenciaEspecialidadePrescritorAsync(model.First().Id))
            .ReturnsAsync(1);

        //Action
        var exception = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<DomainException>(exception);
        _especialidadePrescritorRepositoryMock
            .Verify(l => l.Remove(It.IsAny<EspecialidadePrescritorModel.EspecialidadePrescritor>()), Times.Never);

        unitOfWork
            .Verify(l => l.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }
}
