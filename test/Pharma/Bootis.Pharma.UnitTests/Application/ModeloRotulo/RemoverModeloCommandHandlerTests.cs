using Bootis.Pharma.UnitTests.Common.Fakes.ModeloRotulo;
using Bootis.Producao.Application.UseCases.ModeloRotulo;
using Bootis.Producao.Domain.AggregatesModel.ModeloRotuloAggregate;
using Bootis.Producao.UnitTests.Fixtures.ModeloRotulo;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;
using UUIDNext;

namespace Bootis.Producao.UnitTests.Application.ModeloRotulo;

public class RemoverModeloCommandHandlerTests : BaseTest
{
    private readonly RemoverRequestHandler _handler;
    private readonly Mock<IModeloRotuloRepository> _modeloRotuloRepository;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public RemoverModeloCommandHandlerTests()
    {
        _modeloRotuloRepository = new Mock<IModeloRotuloRepository>();
        _handler = new RemoverRequestHandler(unitOfWork.Object, _modeloRotuloRepository.Object);
    }

    [Fact]
    public async Task RemoverModeloRotulo_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = RemoverModeloRotuloCommandFake.CreateRemoverModeloRotuloCommandValido();
        var modeloRotulos = new List<Producao.Domain.AggregatesModel.ModeloRotuloAggregate.ModeloRotulo>
        {
            ModeloRotuloFake.CreateModeloRotulo()
        };

        cmd.Id.Add(modeloRotulos.First().Id);

        _modeloRotuloRepository.Setup(l => l.ObterPorIdsAsync(cmd.Id)).Returns(
            Task.FromResult(modeloRotulos));

        _modeloRotuloRepository.Setup(l => l.Remove(modeloRotulos.First()))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _modeloRotuloRepository
            .Verify(
                l => l.Remove(
                    It.Is<Producao.Domain.AggregatesModel.ModeloRotuloAggregate.ModeloRotulo>(m =>
                        m.Id == modeloRotulos.First().Id)), Times.Once);
    }

    [Fact]
    public async Task RemoverModeloRotulo_ExecutadoComErro()
    {
        //Arrange
        var cmd = RemoverModeloRotuloCommandFake.CreateRemoverModeloRotuloCommandValido();
        var modeloRotulos = new List<Producao.Domain.AggregatesModel.ModeloRotuloAggregate.ModeloRotulo>();

        cmd.Id.Add(Uuid.NewSequential());

        _modeloRotuloRepository.Setup(l => l.ObterPorIdsAsync(cmd.Id)).Returns(
            Task.FromResult(modeloRotulos));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _modeloRotuloRepository.Verify(
            l => l.Remove(
                It.Is<Producao.Domain.AggregatesModel.ModeloRotuloAggregate.ModeloRotulo>(m =>
                    m.Id == modeloRotulos.First().Id)),
            Times.Never);
    }
}
