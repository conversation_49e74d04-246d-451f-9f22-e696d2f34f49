using Bootis.Estoque.Application.UseCases.MovimentoEstoque;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Estoque.UnitTests.Fixtures.MovimentoEstoque;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.UnitTests;
using Moq;
using UUIDNext;
using Movimento = Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate;

namespace Bootis.Estoque.UnitTests.Application.MovimentoEstoque;

public class RemoverMovimentoEstoqueCommandHandlerTests : BaseTest
{
    private readonly Mock<Movimento.IMovimentoEstoqueRepository> _movimentoEstoqueRepositoryMock;

    private readonly RemoverRequestHandler _removerMovimentoEstoqueCommandHandler;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public RemoverMovimentoEstoqueCommandHandlerTests()
    {
        _movimentoEstoqueRepositoryMock = new Mock<Movimento.IMovimentoEstoqueRepository>();

        _removerMovimentoEstoqueCommandHandler =
            new RemoverRequestHandler(unitOfWork.Object, _movimentoEstoqueRepositoryMock.Object);
    }

    [Fact]
    public async Task RemoverMovimentoEstoqueCommand_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = RemoverMovimentoEstoqueCommandFake.CreateRemoverMovimentoEstoqueCommandValido();

        var movimentoEstoque = new Movimento.MovimentoEstoque(DateTime.UtcNow, 1.ToGuid(), 1.ToGuid(), 1.ToGuid(),
            TipoOperacao.Entrada, 1.ToGuid(), 1,
            UnidadeMedidaAbreviacao.mg, Uuid.NewSequential());

        _movimentoEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.MovimentoEstoqueId)).Returns(
            Task.FromResult(movimentoEstoque));

        _movimentoEstoqueRepositoryMock.Setup(l => l.Remove(movimentoEstoque))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default)).Returns(
            Task.FromResult(1));

        //Action
        await _removerMovimentoEstoqueCommandHandler.Handle(cmd, default);

        //Assert
        _movimentoEstoqueRepositoryMock.Verify(
            l => l.Remove(It.Is<Movimento.MovimentoEstoque>(m => m.EmpresaId == movimentoEstoque.EmpresaId)),
            Times.Once);
    }

    [Fact]
    public async Task RemoverLoteCommand_ExecutadoComErro()
    {
        //Arrange
        var cmd = RemoverMovimentoEstoqueCommandFake.CreateRemoverMovimentoEstoqueCommandValido();

        var movimentoEstoque = new Movimento.MovimentoEstoque(DateTime.UtcNow, 1.ToGuid(), 1.ToGuid(), 1.ToGuid(),
            TipoOperacao.Entrada, 1.ToGuid(), 1,
            UnidadeMedidaAbreviacao.mg, Uuid.NewSequential());

        _movimentoEstoqueRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.MovimentoEstoqueId)).Returns(
            Task.FromResult<Movimento.MovimentoEstoque>(default));

        _movimentoEstoqueRepositoryMock.Setup(l => l.Remove(movimentoEstoque))
            .Verifiable();

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _removerMovimentoEstoqueCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _movimentoEstoqueRepositoryMock.Verify(
            l => l.Remove(It.Is<Movimento.MovimentoEstoque>(m => m.EmpresaId == movimentoEstoque.EmpresaId)),
            Times.Never);
    }
}
