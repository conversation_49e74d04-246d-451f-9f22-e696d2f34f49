using Bootis.Pessoa.Application.Requests.Fornecedor.Atualizar;
using Bootis.Pessoa.Application.UseCases.Fornecedor;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;
using UUIDNext;
using FornecedorAggregate = Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;

namespace Bootis.Pessoa.UnitTests.Application.Fornecedor;

public class AtualizarStatusRequestHandlerTests : BaseTest
{
    private readonly Mock<FornecedorAggregate.IFornecedorRepository> _fornecedorRepositoryMock;
    private readonly AtualizarStatusRequestHandler _handler;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarStatusRequestHandlerTests()
    {
        _fornecedorRepositoryMock = new Mock<FornecedorAggregate.IFornecedorRepository>();
        _handler = new AtualizarStatusRequestHandler(unitOfWork.Object, _fornecedorRepositoryMock.Object);
    }

    [Fact]
    public async Task AtualizarStatusFornecedor_ExecutadoComSucesso()
    {
        //Arrange
        var request = new AtualizarStatusRequest
        {
            FornecedoresId = new List<Guid> { Uuid.NewSequential() },
            Ativo = true
        };

        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        _fornecedorRepositoryMock.Setup(r => r.ObterPorIdAsync(It.IsAny<Guid>()))
            .ReturnsAsync(fornecedor);

        _fornecedorRepositoryMock.Setup(r => r.Update(fornecedor))
            .Verifiable();

        unitOfWork.Setup(r => r.SaveChangesAsync(default))
            .ReturnsAsync(1);

        //Action
        await _handler.Handle(request, default);

        //Assert
        unitOfWork.Verify(r => r.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task AtualizarStatusFornecedor_ExecutadoComErro()
    {
        //Arrange
        var request = new AtualizarStatusRequest
        {
            FornecedoresId = new List<Guid> { Uuid.NewSequential() },
            Ativo = true
        };

        _fornecedorRepositoryMock.Setup(r => r.ObterSemInclusoesAsync(It.IsAny<Guid>()))
            .Returns(Task.FromResult<FornecedorAggregate.Fornecedor>(null));

        //Action
        var exception = await Assert.ThrowsAsync<ValidationException>(() => _handler
            .Handle(request, default));

        //Assert
        Assert.IsType<ValidationException>(exception);
        unitOfWork.Verify(r => r.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }
}
