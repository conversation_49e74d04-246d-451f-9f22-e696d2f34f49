using Bootis.Estoque.Application.UseCases.LocalEstoque;
using Bootis.Estoque.UnitTests.Fixtures.LocalEstoque;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;
using LocalEstoqueModel = Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;

namespace Bootis.Estoque.UnitTests.Application.LocalEstoque;

public class AtualizarLocalEstoqueCommandHandlerTests : BaseTest
{
    private readonly Mock<IEmpresaRepository> _empresaRepository;
    private readonly AtualizarRequestHandler _handler;
    private readonly Mock<LocalEstoqueModel.ILocalEstoqueRepository> _localEstoqueRepository;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarLocalEstoqueCommandHandlerTests()
    {
        _empresaRepository = new Mock<IEmpresaRepository>();
        _localEstoqueRepository = new Mock<LocalEstoqueModel.ILocalEstoqueRepository>();
        _handler = new AtualizarRequestHandler(unitOfWork.Object, _localEstoqueRepository.Object,
            _empresaRepository.Object);
    }

    [Fact]
    public async Task AtualizarLocalEstoque_ExecutadoComSucesso()
    {
        //Arrange
        var empresa = EmpresaFakes.GerarEmpresaValido();
        var cmd = AtualizarLocalEstoqueCommandFake.CriarCommandValido();
        var localEstoque = new Mock<LocalEstoqueModel.LocalEstoque>();

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId))
            .Returns(Task.FromResult(localEstoque.Object));

        _localEstoqueRepository.Setup(l =>
                l.ObterLocalEstoquePorDescricaoAsync(cmd.Descricao, cmd.LocalEstoqueId, empresa.Id))
            .Returns(Task.FromResult<LocalEstoqueModel.LocalEstoque>(default));

        _empresaRepository.Setup(l => l.ObterPorIdAsync(cmd.EmpresaId)).Returns(
            Task.FromResult(empresa));

        _localEstoqueRepository.Setup(l => l.Update(localEstoque.Object))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _localEstoqueRepository
            .Verify(l => l.Update(It.Is<LocalEstoqueModel.LocalEstoque>(m => m.Descricao == cmd.Descricao)),
                Times.Once);
    }

    [Fact]
    public async Task AtualizarLocalEstoque_ExecutadoComErro_GuidNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarLocalEstoqueCommandFake.CriarCommandValido();
        var localEstoque = new Mock<LocalEstoqueModel.LocalEstoque>();

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId))
            .Returns(Task.FromResult<LocalEstoqueModel.LocalEstoque>(default));

        _localEstoqueRepository.Setup(l => l.Update(localEstoque.Object))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _localEstoqueRepository.Verify(
            l => l.Update(It.Is<LocalEstoqueModel.LocalEstoque>(m => m.Descricao == cmd.Descricao)),
            Times.Never);
    }

    [Fact]
    public async Task AtualizarLocalEstoque_ExecutadoComErro_DescricaoExistente()
    {
        //Arrange
        var cmd = AtualizarLocalEstoqueCommandFake.CriarCommandValido();
        var localEstoque = new Mock<LocalEstoqueModel.LocalEstoque>();
        var empresa = EmpresaFakes.GerarEmpresaValido();

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId)).Returns(
            Task.FromResult(localEstoque.Object));

        _localEstoqueRepository.Setup(l =>
                l.ObterLocalEstoquePorDescricaoAsync(cmd.Descricao, cmd.LocalEstoqueId, empresa.Id))
            .Returns(Task.FromResult(localEstoque.Object));

        _localEstoqueRepository.Setup(l => l.Update(localEstoque.Object))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _localEstoqueRepository.Verify(
            l => l.Update(It.Is<LocalEstoqueModel.LocalEstoque>(m => m.Descricao == cmd.Descricao)),
            Times.Never);
    }

    [Fact]
    public async Task AtualizarLocalEstoque_ExecutadoComErro_EmpresaGuidNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarLocalEstoqueCommandFake.CriarCommandValido();
        var localEstoque = new Mock<LocalEstoqueModel.LocalEstoque>();
        var empresa = EmpresaFakes.GerarEmpresaValido();

        _localEstoqueRepository.Setup(l => l.ObterPorIdAsync(cmd.LocalEstoqueId))
            .Returns(Task.FromResult(localEstoque.Object));

        _localEstoqueRepository.Setup(l =>
                l.ObterLocalEstoquePorDescricaoAsync(cmd.Descricao, cmd.LocalEstoqueId, empresa.Id))
            .Returns(Task.FromResult<LocalEstoqueModel.LocalEstoque>(default));

        _empresaRepository.Setup(l => l.ObterPorIdAsync(cmd.EmpresaId))
            .Returns(Task.FromResult<Empresa>(default));

        _localEstoqueRepository.Setup(l => l.Update(localEstoque.Object))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _localEstoqueRepository.Verify(
            l => l.Update(It.Is<LocalEstoqueModel.LocalEstoque>(m => m.Descricao == cmd.Descricao)),
            Times.Never);
    }
}
