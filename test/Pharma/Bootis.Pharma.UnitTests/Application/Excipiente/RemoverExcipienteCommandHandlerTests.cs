using Bootis.Catalogo.Application.Requests.ProdutoExcipiente.Remover;
using Bootis.Catalogo.Application.UseCases.ProdutoExcipiente;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoExcipienteAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;
using Moq;
using UUIDNext;

namespace Bootis.Producao.UnitTests.Application.Excipiente;

public class RemoverExcipienteCommandHandlerTests : BaseTest
{
    private readonly Mock<IExcipienteRepository> _excipienteRepository;

    private readonly RemoverRequestHandler _handler;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public RemoverExcipienteCommandHandlerTests()
    {
        _excipienteRepository = new Mock<IExcipienteRepository>();

        unitOfWork.Setup(l => l.GetRepository<IExcipienteRepository>())
            .Returns(_excipienteRepository.Object);

        _handler = new RemoverRequestHandler(unitOfWork.Object);
    }

    [Fact]
    public async Task CadastrarExcipiente_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = new RemoverRequest { Id = Uuid.NewSequential() };

        var excipiente = new ProdutoExcipiente(TipoClasseBiofarmaceutica.ClasseI, 1.ToGuid(), 1);

        _excipienteRepository.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(excipiente));

        _excipienteRepository.Setup(l => l.Remove(excipiente))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _excipienteRepository.Verify(
            l => l.Remove(It.Is<ProdutoExcipiente>(m => m.Biofarmaceutica == excipiente.Biofarmaceutica)),
            Times.Once);
    }

    [Fact]
    public async Task CadastrarExcipiente_ExecutadoComErro()
    {
        //Arrange
        var cmd = new RemoverRequest { Id = Uuid.NewSequential() };

        var excipiente = new ProdutoExcipiente(TipoClasseBiofarmaceutica.ClasseI, 1.ToGuid(), 1);

        _excipienteRepository.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult<ProdutoExcipiente>(default));

        _excipienteRepository.Setup(l => l.Remove(excipiente))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _excipienteRepository.Verify(
            l => l.Add(It.Is<ProdutoExcipiente>(m => m.Biofarmaceutica == excipiente.Biofarmaceutica)),
            Times.Never);
    }
}
