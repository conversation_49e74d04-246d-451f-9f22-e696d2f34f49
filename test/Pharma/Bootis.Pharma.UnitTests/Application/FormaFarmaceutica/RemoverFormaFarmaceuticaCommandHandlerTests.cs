using Bootis.Catalogo.Application.UseCases.FormaFarmaceutica;
using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Estoque.UnitTests.Fixtures.FormaFarmaceutica;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Producao.UnitTests.Application.FormaFarmaceutica;

public class RemoverFormaFarmaceuticaCommandHandlerTests : BaseTest
{
    private readonly Mock<IFormaFarmaceuticaRepository> _formaFarmaceuticaRepositoryMock;
    private readonly RemoverRequestHandler _removerFormaFarmaceuticaCommandHandler;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public RemoverFormaFarmaceuticaCommandHandlerTests()
    {
        _formaFarmaceuticaRepositoryMock = new Mock<IFormaFarmaceuticaRepository>();

        unitOfWork.Setup(l => l.GetRepository<IFormaFarmaceuticaRepository>())
            .Returns(_formaFarmaceuticaRepositoryMock.Object);

        _removerFormaFarmaceuticaCommandHandler =
            new RemoverRequestHandler(unitOfWork.Object);
    }

    [Fact]
    public async Task RemoverFormaFarmaceutica_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = RemoverFormaFarmaceuticaCommandFake.CreateRemoverFormaFarmaceuticaCommandValido();
        var formasFarmaceuticas = new List<Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica>
        {
            FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo()
        };

        cmd.Id.Add(formasFarmaceuticas.First().Id);

        _formaFarmaceuticaRepositoryMock.Setup(l => l.ObterPorIdsAsync(cmd.Id)).Returns(
            Task.FromResult(formasFarmaceuticas));

        _formaFarmaceuticaRepositoryMock.Setup(l => l.Remove(formasFarmaceuticas.First())).Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _removerFormaFarmaceuticaCommandHandler.Handle(cmd, default);

        //Assert
        _formaFarmaceuticaRepositoryMock.Verify(
            l => l.Remove(
                It.Is<Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica>(m =>
                    m.Descricao == formasFarmaceuticas.First().Descricao)),
            Times.Once);
    }

    [Fact]
    public async Task RemoverFormaFarmaceutica_ExecutadoComErro_GuidNaoEncontrado()
    {
        //Arrange
        var cmd = RemoverFormaFarmaceuticaCommandFake.CreateRemoverFormaFarmaceuticaCommandValido();
        var formasFarmaceuticas =
            new List<Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica>();
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();

        cmd.Id.Add(formaFarmaceutica.Id);

        _formaFarmaceuticaRepositoryMock.Setup(l => l.ObterPorIdsAsync(cmd.Id)).Returns(
            Task.FromResult(formasFarmaceuticas));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _removerFormaFarmaceuticaCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _formaFarmaceuticaRepositoryMock.Verify(
            l => l.Remove(
                It.Is<Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica>(m =>
                    m.Id == formasFarmaceuticas.First().Id)),
            Times.Never);
    }

    [Fact]
    public async Task RemoverFormaFarmaceutica_ComDependencias_ExecutadoComErro()
    {
        //Arrange
        var cmd = RemoverFormaFarmaceuticaCommandFake.CreateRemoverFormaFarmaceuticaCommandValido();
        var retornoDependencia = new List<(Guid Id, string Descricao)>();
        var formasFarmaceuticas = new List<Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica>
        {
            FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo()
        };

        cmd.Id.Add(formasFarmaceuticas.First().Id);

        retornoDependencia.Add((new Guid(), "Forma"));

        _formaFarmaceuticaRepositoryMock.Setup(l => l.ObterPorIdsAsync(cmd.Id)).Returns(
            Task.FromResult(formasFarmaceuticas));

        _formaFarmaceuticaRepositoryMock.Setup(l => l.VerificarDependenciaAsync(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(retornoDependencia.AsEnumerable()));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _removerFormaFarmaceuticaCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<DomainException>(executionResult);
        _formaFarmaceuticaRepositoryMock.Verify(
            l => l.Remove(
                It.Is<Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica>(m =>
                    m.Id == formasFarmaceuticas.First().Id)),
            Times.Never);
    }
}
