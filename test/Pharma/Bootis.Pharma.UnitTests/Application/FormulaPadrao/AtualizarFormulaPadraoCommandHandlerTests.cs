using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Pharma.UnitTests.Common.Fakes.FormulaPadrao;
using Bootis.Pharma.UnitTests.Common.Fakes.FormulaPadraoItem;
using Bootis.Producao.Application.UseCases.FormulaPadrao;
using Bootis.Producao.Domain.AggregatesModel.FormulaPadraoAggregate;
using Bootis.Producao.UnitTests.Fixtures.FormulaPadrao;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Producao.UnitTests.Application.FormulaPadrao;

public class AtualizarFormulaPadraoCommandHandlerTests : BaseTest
{
    private readonly AtualizarRequestHandler _atualizarFormulaPadraoCommandHandler;
    private readonly Mock<IFormaFarmaceuticaRepository> _formaFarmaceuticaRepositoryMock;
    private readonly Mock<IFormulaPadraoRepository> _formulaPadraoRepositoryMock;
    private readonly Mock<IProdutoRepository> _produtoRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarFormulaPadraoCommandHandlerTests()
    {
        _formulaPadraoRepositoryMock = new Mock<IFormulaPadraoRepository>();
        _formaFarmaceuticaRepositoryMock = new Mock<IFormaFarmaceuticaRepository>();
        _produtoRepositoryMock = new Mock<IProdutoRepository>();
        _atualizarFormulaPadraoCommandHandler = new AtualizarRequestHandler(unitOfWork.Object,
            _formulaPadraoRepositoryMock.Object,
            _produtoRepositoryMock.Object, _formaFarmaceuticaRepositoryMock.Object);
    }

    [Fact]
    public async Task AtualizarFormulaPadrao_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = AtualizarFormulaPadraoCommandFake.CreateAtualizarFormulaPadraoCommandValido();
        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var formulaPadrao = FormulaPadraoFake.CreateFormulaPadrao();
        var formulaPadraoItem = FormulaPadraoItemFake.CreateFormulaPadraoItem();
        var produtosFormulaPadraoItem = new List<Produto>
        {
            ProdutoFake.CreateProdutoMateriaPrimaFake()
        };

        cmd.Produtos.First().Id = formulaPadraoItem.Id;
        cmd.Produtos.First().ProdutoId = produtosFormulaPadraoItem.First().Id;
        formulaPadrao.AdicionarItem(formulaPadraoItem);

        _formulaPadraoRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(formulaPadrao));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoId)).Returns(
            Task.FromResult(produto));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorIds(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(produtosFormulaPadraoItem));

        _formaFarmaceuticaRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.FormaFarmaceuticaId)).Returns(
            Task.FromResult(formaFarmaceutica));

        _formulaPadraoRepositoryMock.Setup(l => l.Update(formulaPadrao))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _atualizarFormulaPadraoCommandHandler.Handle(cmd, default);

        //Assert
        _formulaPadraoRepositoryMock.Verify(
            l => l.Update(
                It.Is<Producao.Domain.AggregatesModel.FormulaPadraoAggregate.FormulaPadrao>(m =>
                    m.Procedimento == formulaPadrao.Procedimento)),
            Times.Once);
    }

    [Fact]
    public async Task AtualizarFormulaPadrao_ExecutadoComSucesso_CadastroDeProduto()
    {
        //Arrange
        var cmd = AtualizarFormulaPadraoCommandFake.CreateAtualizarFormulaPadraoCommandValido_CadastroDeProduto();
        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var formulaPadrao = FormulaPadraoFake.CreateFormulaPadrao();
        var formulaPadraoItem = FormulaPadraoItemFake.CreateFormulaPadraoItem();
        var produtosFormulaPadraoItem = new List<Produto>
        {
            ProdutoFake.CreateProdutoFake_Embalagem()
        };

        formulaPadrao.AdicionarItem(formulaPadraoItem);
        cmd.Produtos.First().ProdutoId = produtosFormulaPadraoItem.First().Id;

        _formulaPadraoRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(formulaPadrao));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoId)).Returns(
            Task.FromResult(produto));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorIds(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(produtosFormulaPadraoItem));

        _formaFarmaceuticaRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.FormaFarmaceuticaId)).Returns(
            Task.FromResult(formaFarmaceutica));

        _formulaPadraoRepositoryMock.Setup(l => l.Update(formulaPadrao))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _atualizarFormulaPadraoCommandHandler.Handle(cmd, default);

        //Assert
        _formulaPadraoRepositoryMock.Verify(
            l => l.Update(
                It.Is<Producao.Domain.AggregatesModel.FormulaPadraoAggregate.FormulaPadrao>(m =>
                    m.Procedimento == formulaPadrao.Procedimento)),
            Times.Once);
    }

    [Fact]
    public async Task AtualizarFormulaPadrao_ExecutadoComErro_GuidNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarFormulaPadraoCommandFake.CreateAtualizarFormulaPadraoCommandValido();
        var formulaPadrao = FormulaPadraoFake.CreateFormulaPadrao();

        _formulaPadraoRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult<Producao.Domain.AggregatesModel.FormulaPadraoAggregate.FormulaPadrao>(default));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _atualizarFormulaPadraoCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _formulaPadraoRepositoryMock.Verify(
            l => l.Update(
                It.Is<Producao.Domain.AggregatesModel.FormulaPadraoAggregate.FormulaPadrao>(m =>
                    m.Procedimento == formulaPadrao.Procedimento)),
            Times.Never);
    }

    [Fact]
    public async Task AtualizarFormulaPadrao_ExecutadoComErro_ProdutoNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarFormulaPadraoCommandFake.CreateAtualizarFormulaPadraoCommandValido();
        var formulaPadrao = FormulaPadraoFake.CreateFormulaPadrao();

        _formulaPadraoRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(formulaPadrao));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoId)).Returns(
            Task.FromResult<Produto>(default));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _atualizarFormulaPadraoCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _formulaPadraoRepositoryMock.Verify(
            l => l.Update(
                It.Is<Producao.Domain.AggregatesModel.FormulaPadraoAggregate.FormulaPadrao>(m =>
                    m.Procedimento == formulaPadrao.Procedimento)),
            Times.Never);
    }

    [Fact]
    public async Task AtualizarFormulaPadrao_ExecutadoComErro_FormaFarmaceuticaNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarFormulaPadraoCommandFake.CreateAtualizarFormulaPadraoCommandValido();
        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();
        var formulaPadrao = FormulaPadraoFake.CreateFormulaPadrao();
        var produtosFormulaPadraoItem = new List<Produto>
        {
            ProdutoFake.CreateProdutoFake_Embalagem()
        };

        cmd.Produtos.First().ProdutoId = produtosFormulaPadraoItem.First().Id;

        _formulaPadraoRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(formulaPadrao));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoId)).Returns(
            Task.FromResult(produto));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorIds(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(produtosFormulaPadraoItem));

        _formaFarmaceuticaRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.FormaFarmaceuticaId)).Returns(
            Task.FromResult<Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate.FormaFarmaceutica>(
                default));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _atualizarFormulaPadraoCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _formulaPadraoRepositoryMock.Verify(
            l => l.Update(
                It.Is<Producao.Domain.AggregatesModel.FormulaPadraoAggregate.FormulaPadrao>(m =>
                    m.Procedimento == formulaPadrao.Procedimento)),
            Times.Never);
    }

    [Fact]
    public async Task AtualizarFormulaPadrao_ExecutadoComErro_QuantidadePadraoInvalida()
    {
        //Arrange
        var cmd = AtualizarFormulaPadraoCommandFake.CreateAtualizarFormulaPadraoCommandValido_QuantidadeInvalida();
        var produto = ProdutoFake.CreateProdutoMateriaPrimaFake();
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var formulaPadrao = FormulaPadraoFake.CreateFormulaPadrao();
        var produtosFormulaPadraoItem = new List<Produto>
        {
            ProdutoFake.CreateProdutoFake_Embalagem()
        };

        cmd.Produtos.First().ProdutoId = produtosFormulaPadraoItem.First().Id;

        _formulaPadraoRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(formulaPadrao));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorId(cmd.ProdutoId)).Returns(
            Task.FromResult(produto));

        _produtoRepositoryMock.Setup(l => l.ObterProdutoPorIds(It.IsAny<IEnumerable<Guid>>())).Returns(
            Task.FromResult(produtosFormulaPadraoItem));

        _formaFarmaceuticaRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.FormaFarmaceuticaId)).Returns(
            Task.FromResult(formaFarmaceutica));


        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _atualizarFormulaPadraoCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<DomainException>(executionResult);
    }
}
