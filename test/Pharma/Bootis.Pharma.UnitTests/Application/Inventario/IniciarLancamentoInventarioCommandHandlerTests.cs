using Bootis.Estoque.Application.UseCases.Inventario;
using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.UnitTests.Fixtures.Inventario;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Estoque.UnitTests.Application.Inventario;

public class IniciarLancamentoInventarioCommandHandlerTests : BaseTest
{
    private readonly IniciarLancamentoRequestHandler _handler;
    private readonly Mock<IInventarioRepository> _inventarioRepositoryMock;
    private readonly Mock<IUsuarioRepository> _usuarioRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public IniciarLancamentoInventarioCommandHandlerTests()
    {
        _inventarioRepositoryMock = new Mock<IInventarioRepository>();
        _usuarioRepositoryMock = new Mock<IUsuarioRepository>();
        _handler = new IniciarLancamentoRequestHandler(unitOfWork.Object, UserContext.Object,
            _inventarioRepositoryMock.Object,
            _usuarioRepositoryMock.Object);
    }

    [Fact]
    public async Task IniciarLancamentoInventario_ExecutadoComSucesso()
    {
        //Arrange 
        var cmd = IniciarLancamentoInventarioCommandFake.CreateIniciarLancamentoInventarioCommandValido();
        var inventario = InventarioFake.CriarInventarioValido();
        IEnumerable<SaldoEstoque> saldosEstoque =
        [
            SaldoEstoqueFake.CreateSaldoEstoqueValido(),
            SaldoEstoqueFake.CreateSaldoEstoqueValido()
        ];

        _inventarioRepositoryMock.Setup(l => l.ObterInventarioPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(inventario));

        _inventarioRepositoryMock.Setup(l => l.ObterSaldosEstoqueVinculadosAsync(cmd.Id)).Returns(
            Task.FromResult(saldosEstoque));

        _inventarioRepositoryMock.Setup(l => l.Update(inventario))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default)).Returns(
            Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _inventarioRepositoryMock.Verify(
            l => l.Update(
                It.Is<Estoque.Domain.AggregatesModel.InventarioAggregate.Inventario>(m =>
                    m.StatusInventario == inventario.StatusInventario)),
            Times.Once);
    }
}
