using Bootis.Estoque.Application.UseCases.Inventario;
using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.UnitTests.Fixtures.Inventario;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Estoque.UnitTests.Application.Inventario;

public class RemoverInventarioCommandHandlerTests : BaseTest
{
    private readonly RemoverRequestHandler _handler;
    private readonly Mock<IInventarioRepository> _inventarioRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public RemoverInventarioCommandHandlerTests()
    {
        _inventarioRepositoryMock = new Mock<IInventarioRepository>();
        _handler = new RemoverRequestHandler(unitOfWork.Object, _inventarioRepositoryMock.Object);
    }

    [Fact]
    public async Task RemoverInventario_ExecutadoComSucesso()
    {
        //Arrange 
        var cmd = RemoverInventarioCommandFake.CreateRemoverInventarioCommandValido();
        var inventario = InventarioFake.CriarInventarioValido();

        _inventarioRepositoryMock.Setup(l => l.ObterInventarioPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(inventario));

        _inventarioRepositoryMock.Setup(l => l.Remove(inventario)).Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default)).Returns(
            Task.FromResult(1));

        //Action
        await _handler.Handle(cmd, default);

        //Assert
        _inventarioRepositoryMock.Verify(
            l => l.Remove(It.Is<Estoque.Domain.AggregatesModel.InventarioAggregate.Inventario>(m =>
                m.CodigoUltimoLancamento == inventario.CodigoUltimoLancamento)),
            Times.Once);
    }

    [Fact]
    public async Task RemoverInventario_StatusInvalido_ExecutadoComErro()
    {
        //Arrange 
        var cmd = RemoverInventarioCommandFake.CreateRemoverInventarioCommandValido();
        var inventario = InventarioFake.CriarInventarioValido();
        var saldosEstoque = new List<SaldoEstoque> { SaldoEstoqueFake.CreateSaldoEstoqueValido() };

        inventario.IniciarLancamento(saldosEstoque, 1.ToGuid());

        _inventarioRepositoryMock.Setup(l => l.ObterInventarioPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(inventario));

        //Action
        var executionResult = await Record.ExceptionAsync(() => _handler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _inventarioRepositoryMock.Verify(
            l => l.Add(It.Is<Estoque.Domain.AggregatesModel.InventarioAggregate.Inventario>(m =>
                m.CodigoUltimoLancamento == inventario.CodigoUltimoLancamento)),
            Times.Never);
    }
}
