using Bootis.Catalogo.Application.UseCases.ProdutoDiluido;
using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoDiluidoAggregate;
using Bootis.Estoque.UnitTests.Fixtures.ProdutoDiluido;
using Bootis.Pharma.UnitTests.Common.Fakes;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.UnitTests;
using Moq;

namespace Bootis.Estoque.UnitTests.Application.ProdutoDiluido;

public class AtualizarProdutoDiluidoCommandHandlerTests : BaseTest
{
    private readonly AtualizarRequestHandler _atualizarProdutoDiluidoCommandHandler;
    private readonly Mock<IFormaFarmaceuticaRepository> _formaFarmaceuticaRepositoryMock;
    private readonly Mock<IProdutoDiluidoRepository> _produtoDiluidoRepositoryMock;
    private readonly Mock<IUnitOfWork> unitOfWork = new();

    public AtualizarProdutoDiluidoCommandHandlerTests()
    {
        _produtoDiluidoRepositoryMock = new Mock<IProdutoDiluidoRepository>();
        _formaFarmaceuticaRepositoryMock = new Mock<IFormaFarmaceuticaRepository>();

        unitOfWork.Setup(l => l.GetRepository<IProdutoDiluidoRepository>())
            .Returns(_produtoDiluidoRepositoryMock.Object);
        unitOfWork.Setup(l => l.GetRepository<IFormaFarmaceuticaRepository>())
            .Returns(_formaFarmaceuticaRepositoryMock.Object);

        _atualizarProdutoDiluidoCommandHandler = new AtualizarRequestHandler(unitOfWork.Object);
    }

    [Fact]
    public async Task AtualizarProdutoDiluido_ExecutadoComSucesso()
    {
        //Arrange
        var cmd = AtualizarProdutoDiluidoCommandFake.CreateAtualizarProdutoDiluidoCommandValido();
        var formaFarmaceutica = FormaFarmaceuticaFake.CreateFormaFarmaceutica_Ativo();
        var model = ProdutoDiluidoFake.CreateProdutoDiluidoValido();

        _produtoDiluidoRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(model));

        _formaFarmaceuticaRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.FormaFarmaceuticaId.Value))
            .Returns(
                Task.FromResult(formaFarmaceutica));

        _produtoDiluidoRepositoryMock.Setup(l => l.Update(model))
            .Verifiable();

        unitOfWork.Setup(l => l.SaveChangesAsync(default))
            .Returns(Task.FromResult(1));

        //Action
        await _atualizarProdutoDiluidoCommandHandler.Handle(cmd, default);

        //Assert
        _produtoDiluidoRepositoryMock
            .Verify(
                l => l.Update(
                    It.Is<Catalogo.Domain.AggregatesModel.ProdutoDiluidoAggregate.ProdutoDiluido>(m =>
                        m.DosagemMaxima == model.DosagemMaxima)),
                Times.Once);
    }

    [Fact]
    public async Task AtualizarProdutoDiluido_ExecutadoComErro_GuidNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarProdutoDiluidoCommandFake.CreateAtualizarProdutoDiluidoCommandValido();
        var model = ProdutoDiluidoFake.CreateProdutoDiluidoValido();

        _produtoDiluidoRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult<Catalogo.Domain.AggregatesModel.ProdutoDiluidoAggregate.ProdutoDiluido>(default));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _atualizarProdutoDiluidoCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _produtoDiluidoRepositoryMock
            .Verify(
                l => l.Update(
                    It.Is<Catalogo.Domain.AggregatesModel.ProdutoDiluidoAggregate.ProdutoDiluido>(m =>
                        m.DosagemMaxima == model.DosagemMaxima)),
                Times.Never);
    }

    [Fact]
    public async Task AtualizarProdutoDiluido_ExecutadoComErro_FormaFarmaceuticaGuidNaoEncontrado()
    {
        //Arrange
        var cmd = AtualizarProdutoDiluidoCommandFake.CreateAtualizarProdutoDiluidoCommandValido();
        var model = ProdutoDiluidoFake.CreateProdutoDiluidoValido();

        _produtoDiluidoRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.Id)).Returns(
            Task.FromResult(model));

        _formaFarmaceuticaRepositoryMock.Setup(l => l.ObterPorIdAsync(cmd.FormaFarmaceuticaId.Value))
            .Returns(Task.FromResult<FormaFarmaceutica>(default));

        //Action
        var executionResult =
            await Record.ExceptionAsync(() => _atualizarProdutoDiluidoCommandHandler.Handle(cmd, default));

        //Assert
        Assert.IsType<ValidationException>(executionResult);
        _produtoDiluidoRepositoryMock
            .Verify(
                l => l.Update(
                    It.Is<Catalogo.Domain.AggregatesModel.ProdutoDiluidoAggregate.ProdutoDiluido>(m =>
                        m.DosagemMaxima == model.DosagemMaxima)),
                Times.Never);
    }
}
