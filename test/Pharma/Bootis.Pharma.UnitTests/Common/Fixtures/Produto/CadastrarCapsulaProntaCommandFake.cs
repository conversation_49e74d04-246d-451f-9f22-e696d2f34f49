using Bootis.Catalogo.Application.Requests.Produto.Cadastrar;
using Bootis.Catalogo.Domain.Dtos.Produto.ProdutoCapsulaPronta;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using UUIDNext;

namespace Bootis.Estoque.UnitTests.Fixtures.Produto;

public class CadastrarCapsulaProntaCommandFake
{
    public static CadastrarCapsulaProntaRequest CreateCommandValido()
    {
        return new CadastrarCapsulaProntaRequest
        {
            ClasseProdutoId = TipoClasseProdutoAbreviacao.CapsulaPronta,
            ControlaLote = true,
            ControlaQualidade = true,
            Descricao = "Produto",
            DescricaoRotulo = "Rotulo",
            Etiqueta = false,
            FornecedorId = Uuid.NewSequential(),
            SubGrupoId = Uuid.NewSequential(),
            MargemLucro = 100,
            UnidadeEstoqueId = UnidadeMedidaAbreviacao.un,
            UsoContinuo = false,
            ValorCusto = 50,
            ValorVenda = 100,
            DesativarProjecaoEstoque = false,
            EstoqueMinimo = 1,
            EstoqueMaximo = 10,
            CapsulaTamanhoId = 1.ToGuid(),
            MateriaPrimaAssociacao = new List<ProdutoCapsulaProntaMateriaPrimaAssociacaoDto>
            {
                new()
                {
                    ProdutoMateriaPrimaId = Guid.Empty,
                    UnidadeId = UnidadeMedidaAbreviacao.kg,
                    Quantidade = 1
                }
            }
        };
    }

    public static CadastrarCapsulaProntaRequest CreateCommandValidoSemFornecedor()
    {
        return new CadastrarCapsulaProntaRequest
        {
            ClasseProdutoId = TipoClasseProdutoAbreviacao.CapsulaPronta,
            ControlaLote = true,
            ControlaQualidade = true,
            Descricao = "Produto",
            DescricaoRotulo = "Rotulo",
            Etiqueta = false,
            FornecedorId = null,
            SubGrupoId = Uuid.NewSequential(),
            MargemLucro = 100,
            UnidadeEstoqueId = UnidadeMedidaAbreviacao.un,
            UsoContinuo = false,
            ValorCusto = 50,
            ValorVenda = 100,
            DesativarProjecaoEstoque = false,
            EstoqueMinimo = 1,
            EstoqueMaximo = 10,
            CapsulaTamanhoId = 1.ToGuid(),
            MateriaPrimaAssociacao = new List<ProdutoCapsulaProntaMateriaPrimaAssociacaoDto>
            {
                new()
                {
                    ProdutoMateriaPrimaId = null,
                    UnidadeId = null,
                    Quantidade = null
                }
            }
        };
    }

    public static CadastrarCapsulaProntaRequest CreateCommandValidoSemUnidadeMedida()
    {
        return new CadastrarCapsulaProntaRequest
        {
            ClasseProdutoId = TipoClasseProdutoAbreviacao.CapsulaPronta,
            ControlaLote = true,
            ControlaQualidade = true,
            Descricao = "Produto",
            DescricaoRotulo = "Rotulo",
            Etiqueta = false,
            FornecedorId = null,
            SubGrupoId = Uuid.NewSequential(),
            MargemLucro = 100,
            UsoContinuo = false,
            ValorCusto = 50,
            ValorVenda = 100,
            DesativarProjecaoEstoque = false,
            EstoqueMinimo = 1,
            EstoqueMaximo = 10,
            CapsulaTamanhoId = 1.ToGuid()
        };
    }
}
