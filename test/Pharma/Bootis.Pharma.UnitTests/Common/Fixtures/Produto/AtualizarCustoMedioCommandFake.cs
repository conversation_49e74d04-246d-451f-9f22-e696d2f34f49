using Bootis.Catalogo.Application.Requests.Produto.Atualizar;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Estoque.UnitTests.Fixtures.Produto;

public static class AtualizarCustoMedioCommandFake
{
    public static AtualizarCustoMedioRequest CreateAtualizarCustoMedioCommandValido()
    {
        return new AtualizarCustoMedioRequest
        {
            ProdutoId = 1.ToGuid(),
            ValorEntrada = 1,
            QuantidadeEntrada = 1,
            UnidadeMedidaId = UnidadeMedidaAbreviacao.g
        };
    }

    public static AtualizarCustoMedioRequest CreateAtualizarCustoMedioCommandInvalido()
    {
        return new AtualizarCustoMedioRequest
        {
            ProdutoId = 1.ToGuid(),
            ValorEntrada = 0,
            QuantidadeEntrada = 0,
            UnidadeMedidaId = UnidadeMedidaAbreviacao.g
        };
    }
}
