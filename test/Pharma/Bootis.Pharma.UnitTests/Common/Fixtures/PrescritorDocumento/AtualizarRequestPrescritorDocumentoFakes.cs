using Bootis.Pessoa.Application.Requests.Prescritor.Atualizar;
using Bootis.Shared.Common.Extensions;
using UUIDNext;

namespace Bootis.Pessoa.UnitTests.Fixtures.PrescritorDocumento;

public static class AtualizarRequestPrescritorDocumentoFakes
{
    public static AtualizarDocumentoRequest CriarCommandValido()
    {
        return new AtualizarDocumentoRequest
        {
            PrescritorDocumentoId = Uuid.NewSequential(),
            Identificacao = "Teste",
            TipoDocumentoId = 1.ToGuid()
        };
    }
}
