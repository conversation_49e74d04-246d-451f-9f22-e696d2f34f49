using Bootis.Compra.Application.Requests.PedidoCompra.Remover;
using UUIDNext;

namespace Bootis.Compra.UnitTests.Fixtures;

public static class RemoverPedidoCompraCommandFake
{
    public static RemoverRequest CreateRemoverPedidoCompraComandValido()
    {
        var pedidoCompraId = new List<Guid>();

        return new RemoverRequest
        {
            PedidoCompraId = pedidoCompraId
        };
    }

    public static RemoverRequest CreateRemoverPedidoCompraComandValido_GuidAleatorio()
    {
        var pedidoCompraId = new List<Guid>
        {
            Uuid.NewSequential()
        };

        return new RemoverRequest
        {
            PedidoCompraId = pedidoCompraId
        };
    }
}
