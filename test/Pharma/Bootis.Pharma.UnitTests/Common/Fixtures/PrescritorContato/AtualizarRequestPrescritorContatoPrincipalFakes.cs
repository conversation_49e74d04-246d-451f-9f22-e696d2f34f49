using Bootis.Pessoa.Application.Requests.Prescritor.Atualizar;
using UUIDNext;

namespace Bootis.Pessoa.UnitTests.Fixtures.PrescritorContato;

public static class AtualizarRequestPrescritorContatoPrincipalFakes
{
    public static AtualizarContatoPrincipalRequest CriarCommandValido()
    {
        return new AtualizarContatoPrincipalRequest
        {
            PrescritorContatoId = Uuid.NewSequential(),
            Principal = true
        };
    }
}
