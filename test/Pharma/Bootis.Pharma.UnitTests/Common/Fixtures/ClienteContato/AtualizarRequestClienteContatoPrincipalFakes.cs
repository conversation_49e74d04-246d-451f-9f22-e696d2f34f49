using Bootis.Pessoa.Application.Requests.Cliente.Atualizar;
using UUIDNext;

namespace Bootis.Pessoa.UnitTests.Fixtures.ClienteContato;

public static class AtualizarRequestClienteContatoPrincipalFakes
{
    public static AtualizarContatoPrincipalRequest CriarCommandValido()
    {
        return new AtualizarContatoPrincipalRequest
        {
            ClienteContatoId = Uuid.NewSequential(),
            Principal = true
        };
    }
}
