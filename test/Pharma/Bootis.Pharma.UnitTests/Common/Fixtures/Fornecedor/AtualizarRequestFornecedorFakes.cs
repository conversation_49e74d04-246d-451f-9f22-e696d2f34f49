using Bootis.Pessoa.Application.Requests.Fornecedor.Atualizar;
using Bootis.Pessoa.Domain.Enumerations;
using UUIDNext;

namespace Bootis.Pessoa.UnitTests.Fixtures.Fornecedor;

public static class AtualizarRequestFornecedorFakes
{
    public static AtualizarRequest CriarCommandValidoJuridico()
    {
        return new AtualizarRequest
        {
            Id = Uuid.NewSequential(),
            TipoPessoa = TipoPessoa.Juridica,
            Nome = "Fornecedor Jur�dico Teste",
            RazaoSocial = "Loja Teste",
            TipoFornecedorId = 1,
            Observacao = "Obs",
            Cnpj = "11366708000187",
            LinkImagem = ""
        };
    }

    public static AtualizarRequest CriarCommandValidoFisico()
    {
        return new AtualizarRequest
        {
            Id = Uuid.NewSequential(),
            TipoPessoa = TipoPessoa.Fisica,
            Nome = "Fornecedor F�sico Teste",
            TipoFornecedorId = 1,
            Observacao = "Obs",
            Cpf = "86457144004",
            LinkImagem = ""
        };
    }
}
