using Bootis.Pessoa.Application.Requests.Prescritor.Atualizar;
using Bootis.Shared.Common.Extensions;
using UUIDNext;

namespace Bootis.Pessoa.UnitTests.Fixtures.Prescritor;

public class AtualizarRequestPrescritorFakes
{
    public static AtualizarRequest CriarCommandValido(Guid? id = null)
    {
        return new AtualizarRequest
        {
            Id = id ?? Uuid.NewSequential(),
            NomeCompleto = "Prescritor Teste",
            TipoRegistroId = 1.ToGuid(),
            CodigoRegistro = "123",
            UfRegistroId = Uuid.NewSequential(),
            DataNascimento = DateTime.UtcNow.ToDateOnly(),
            DescontoFormulas = 10,
            DescontoProdutosAcabados = 10,
            LinkImagem = "",
            Observacao = "Obs",
            EspecialidadesId = new List<Guid> { Uuid.NewSequential(), Uuid.NewSequential() }
        };
    }
}
