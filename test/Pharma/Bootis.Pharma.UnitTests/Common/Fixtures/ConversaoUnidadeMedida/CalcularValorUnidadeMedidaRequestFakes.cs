using Bootis.Catalogo.Application.Requests.CalculoConversaoUnidadeMedida.Calcular;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Estoque.UnitTests.Fixtures.ConversaoUnidadeMedida;

public class CalcularValorUnidadeMedidaRequestFakes
{
    public static CalcularConversaoValorUnidadeMedidaRequest CriarCommandValido()
    {
        var conversoes = new List<ConversaoValorUnidadeMedidaACalcular>();
        var conversao = new ConversaoValorUnidadeMedidaACalcular
        {
            Quantidade = 1000,
            UnidadeMedidaConversaoId = UnidadeMedidaAbreviacao.g,
            UnidadeMedidaOrigemId = UnidadeMedidaAbreviacao.kg
        };

        conversoes.Add(conversao);

        return new CalcularConversaoValorUnidadeMedidaRequest(conversoes);
    }

    public static CalcularConversaoValorUnidadeMedidaRequest CriarCommandInvalido()
    {
        var conversoes = new List<ConversaoValorUnidadeMedidaACalcular>();
        var conversao = new ConversaoValorUnidadeMedidaACalcular
        {
            Quantidade = 1000,
            UnidadeMedidaConversaoId = UnidadeMedidaAbreviacao.UI,
            UnidadeMedidaOrigemId = UnidadeMedidaAbreviacao.UI
        };

        conversoes.Add(conversao);

        return new CalcularConversaoValorUnidadeMedidaRequest(conversoes);
    }

    public static CalcularConversaoValorUnidadeMedidaRequest CriarCommandSemUnidadeMedida()
    {
        var conversoes = new List<ConversaoValorUnidadeMedidaACalcular>();
        var conversao = new ConversaoValorUnidadeMedidaACalcular
        {
            Quantidade = 1000
        };

        conversoes.Add(conversao);

        return new CalcularConversaoValorUnidadeMedidaRequest(conversoes);
    }
}
