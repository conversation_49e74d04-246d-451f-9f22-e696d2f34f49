using Bootis.Compra.Application.Requests.PedidoCompra.Cadastrar;
using Bootis.Compra.Domain.Dtos.PedidoCompra;
using Bootis.Compra.Domain.Enumerations;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using UUIDNext;

namespace Bootis.Compra.UnitTests.Fixtures;

public static class CadastrarRascunhoPedidoCompraCommandFake
{
    public static CadastrarRascunhoRequest CreateCadastrarRascunhoPedidoCompraComandValido()
    {
        var itemRequest = new List<PedidoCompraItemRascunhoDto>
        {
            new()
            {
                PrecoUnitario = 1,
                ProdutoId = Guid.Empty,
                Quantidade = 1,
                TipoDesconto = TipoDesconto.DescontoMonetario,
                UnidadeMedidaId = UnidadeMedidaAbreviacao.g,
                ValorDescontoUnitario = 1
            }
        };

        return new CadastrarRascunhoRequest
        {
            FornecedorId = Uuid.NewSequential(),
            Frete = 10,
            Observacao = "Teste",
            PrevisaoEntrega = DateTime.UtcNow.ToDateOnly(),
            ValorDesconto = 10,
            TipoDesconto = TipoDesconto.DescontoPorcentagem,
            ValorAdicional = 100,
            PedidoCompraItens = itemRequest
        };
    }

    public static CadastrarRascunhoRequest CreateCadastrarRascunhoPedidoCompraComandValido_DescontoInvalido()
    {
        var itemRequest = new List<PedidoCompraItemRascunhoDto>
        {
            new()
            {
                PrecoUnitario = 1,
                ProdutoId = Guid.Empty,
                Quantidade = 1,
                TipoDesconto = TipoDesconto.DescontoMonetario,
                UnidadeMedidaId = UnidadeMedidaAbreviacao.g,
                ValorDescontoUnitario = 10
            }
        };

        return new CadastrarRascunhoRequest
        {
            FornecedorId = Uuid.NewSequential(),
            Frete = 10,
            Observacao = "Teste",
            PrevisaoEntrega = DateTime.UtcNow.ToDateOnly(),
            ValorDesconto = 10,
            TipoDesconto = TipoDesconto.DescontoPorcentagem,
            ValorAdicional = 100,
            PedidoCompraItens = itemRequest
        };
    }
}
