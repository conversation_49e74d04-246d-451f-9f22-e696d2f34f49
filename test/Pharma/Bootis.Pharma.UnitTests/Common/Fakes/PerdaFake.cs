using Bootis.Estoque.Domain.AggregatesModel.PerdaAggregate;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Pharma.UnitTests.Common.Fakes;

public static class PerdaFake
{
    public static Perda CreatePerdaValido()
    {
        return new Perda(new DateTime(2020, 10, 10).ToDateOnly(), LoteFake.CreateLoteValido().Id,
            ProdutoFake.CreateProdutoAcabadoFake().Id, LocalEstoqueFake.CreateLocalEstoqueValido().Id,
            1, UnidadeMedidaAbreviacao.g, MotivoPerdaFake.CreateMotivoPerdaValido().Id, "Teste Obs",
            UsuarioFakes.GerarUsuarioValido().Id, 1.ToGuid());
    }
}
