using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaPagadoraAggregate;
using Bootis.Organizacional.Domain.ValuesObject;

namespace Bootis.Pharma.UnitTests.Common.Fakes;

public static class EmpresaFakes
{
    public static Empresa GerarEmpresaValido()
    {
        var empresa = new Empresa("RazaoTeste", "FantasiaTeste", "033234567000123",
            1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(), null, null, "1234567",
            "12345678912345", "12345678912", null, null, true, "11999999999");

        empresa.AdicionarEmpresaPagadora(new EmpresaPagadora(Guid.NewGuid()));

        return empresa;
    }

    public static Empresa GerarFilialValido()
    {
        var empresa = new Empresa("RazaoTeste", "FantasiaTeste", "033234567000123",
            2, <PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(), null, null, "1234567",
            "12345678912345", "12345678912", null, null, true, "11999999999");

        empresa.AdicionarEmpresaPagadora(new EmpresaPagadora(Guid.NewGuid()));

        return empresa;
    }

    public static EmpresaPagadora GerarEmpresaPagadoraValido(Guid empresaId)
    {
        return new EmpresaPagadora(empresaId);
    }

    private static Endereco GerarEnderecoValido()
    {
        return new Endereco("49000000", "rua a", 429, "torre a", "atalaia",
            "aracaju", "sergipe");
    }
}
