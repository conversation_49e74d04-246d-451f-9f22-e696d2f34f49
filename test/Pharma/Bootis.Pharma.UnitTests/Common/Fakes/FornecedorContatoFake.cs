using Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;
using Bootis.Shared.Common.Extensions;

namespace Bootis.Pharma.UnitTests.Common.Fakes;

public static class FornecedorContatoFake
{
    public static FornecedorContato CriarCommandValido()
    {
        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        return new FornecedorContato(fornecedor, 1.ToGuid(), "teste", true, "obs");
    }

    public static List<FornecedorContato> CriarCommandValidoLista()
    {
        var fornecedor = FornecedorFakes.CriarFornecedorValido();

        var contato1 = new FornecedorContato(fornecedor, 1.ToGuid(), "Contato 1", true, "obs1");
        var contato2 = new FornecedorContato(fornecedor, 2.ToGuid(), "Contato 2", false, "obs2");
        var contato3 = new FornecedorContato(fornecedor, 3.ToG<PERSON>(), "Contato 3", false, "obs3");

        contato2.AtualizarPrincipal(true);

        return new List<FornecedorContato> { contato1, contato2, contato3 };
    }
}
