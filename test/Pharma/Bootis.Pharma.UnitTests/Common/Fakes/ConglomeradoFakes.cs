using Bootis.Organizacional.Domain.AggregatesModel.ConglomeradoAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Organizacional.Domain.Dtos;
using Bootis.Organizacional.Domain.Enumerations;
using Bootis.Organizacional.Domain.ValuesObject;
using Bootis.Shared.Common.Extensions;

namespace Bootis.Pharma.UnitTests.Common.Fakes;

public static class ConglomeradoFakes
{
    public static Conglomerado GerarConglomeradoValido()
    {
        return new Conglomerado("teste", "razao teste", "nome teste", "08164852000126",
            GerarEndereco<PERSON>(), GerarUsuarioValido(), "6911-7/01", "123456",
            "123456768", "<EMAIL>", "teste.com", "68921356152");
    }

    public static UsuarioDto MapperUsuarioDto(Usuario usuario)
    {
        return new UsuarioDto
        {
            Celular = usuario.Celular,
            Cpf = usuario.Cpf,
            DataNascimento = usuario.DataNascimento ?? default,
            Email = usuario.Email,
            Nome = usuario.Nome,
            Sobrenome = usuario.Sobrenome
        };
    }

    public static EmpresaDto MapperEmpresaDto(Empresa empresa)
    {
        return new EmpresaDto
        {
            Cnae = empresa.Cnae,
            Cnpj = empresa.Cnpj,
            Email = empresa.Email,
            InscricaoEstadual = empresa.InscricaoEstadual,
            InscricaoMunicipal = empresa.InscricaoMunicipal,
            NomeFantasia = empresa.NomeFantasia,
            RazaoSocial = empresa.RazaoSocial,
            Site = empresa.Site,
            Telefone = empresa.Telefone
        };
    }

    public static EnderecoDto MapperEnderecoDto(Endereco endereco)
    {
        return new EnderecoDto
        {
            Cep = endereco.Cep,
            Bairro = endereco.Bairro,
            Cidade = endereco.Cidade,
            Complemento = endereco.Complemento,
            Estado = endereco.Estado,
            Logradouro = endereco.Logradouro,
            Numero = endereco.Numero
        };
    }

    private static Endereco GerarEnderecoValido()
    {
        return new Endereco("49000000", "rua a", 429, "torre a", "atalaia",
            "aracaju", "sergipe");
    }

    private static Usuario GerarUsuarioValido()
    {
        return new Usuario("Usuario", "Teste", "04272056733", TipoUsuario.Comum.Id, DateTime.UtcNow.Date.ToDateOnly(),
            "<EMAIL>",
            "<EMAIL>", null);
    }
}
