using Bootis.Identity.Application.Users;
using Bootis.Identity.Application.Users.Models;
using Bootis.Identity.Domain.Entities;
using Bootis.Identity.Domain.Interfaces;
using Bootis.Shared;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;

using FluentAssertions;
using Moq;
using UUIDNext;
using Xunit;

namespace Bootis.Identity.UnitTests.Application.Users;

public class CreateUserRequestHandlerTests
{
    private readonly Mock<IUnitOfWork> _unitOfWorkMock;
    private readonly Mock<IGroupRepository> _groupRepositoryMock;
    private readonly CreateUserRequestHandler _handler;

    public CreateUserRequestHandlerTests()
    {
        _unitOfWorkMock = new Mock<IUnitOfWork>();
        _groupRepositoryMock = new Mock<IGroupRepository>();
        _unitOfWorkMock.Setup(x => x.GetRepository<IGroupRepository>())
            .Returns(_groupRepositoryMock.Object);
        _handler = new CreateUserRequestHandler(_unitOfWorkMock.Object);
    }

    [Fact]
    public async Task Handle_WithValidRequest_ShouldCreateUserSuccessfully()
    {
        // Arrange
        var request = CreateValidCreateUserRequest();
        var groups = new List<Group>
        {
            new() { Id = request.Groups.First() }
        };

        _groupRepositoryMock.Setup(x => x.GetGroups(It.IsAny<HashSet<Guid>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(groups);

        User capturedUser = null;
        _unitOfWorkMock.Setup(x => x.Add(It.IsAny<User>()))
            .Callback<User>(user => capturedUser = user);

        // Act
        await _handler.Handle(request, CancellationToken.None);

        // Assert
        capturedUser.Should().NotBeNull();
        capturedUser.Id.Should().Be(request.Id);
        capturedUser.Name.Should().Be(request.Name);
        capturedUser.Email.Should().Be(request.Email);
        capturedUser.IsActive.Should().Be(request.IsActive);
        capturedUser.TenantId.Should().Be(request.TenantId);
        capturedUser.GroupTenantId.Should().Be(request.GroupTenantId);
        capturedUser.EmailConfirmed.Should().BeFalse();
        capturedUser.PasswordResetCode.Should().NotBeNull();
        capturedUser.PasswordHash.Should().NotBeNullOrEmpty();

        _unitOfWorkMock.Verify(x => x.Add(It.IsAny<User>()), Times.Once);
        _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithPermissions_ShouldCreateUserWithPermissions()
    {
        // Arrange
        var request = CreateValidCreateUserRequestWithPermissions();
        var groups = new List<Group>
        {
            new() { Id = request.Groups.First() }
        };

        _groupRepositoryMock.Setup(x => x.GetGroups(It.IsAny<HashSet<Guid>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(groups);

        User capturedUser = null;
        IEnumerable<UserPermission> capturedPermissions = null;

        _unitOfWorkMock.Setup(x => x.Add(It.IsAny<User>()))
            .Callback<User>(user => capturedUser = user);
        _unitOfWorkMock.Setup(x => x.AddRange(It.IsAny<IEnumerable<UserPermission>>()))
            .Callback<IEnumerable<UserPermission>>(permissions => capturedPermissions = permissions);

        // Act
        await _handler.Handle(request, CancellationToken.None);

        // Assert
        capturedUser.Should().NotBeNull();
        capturedUser.UserPermissions.Should().HaveCount(request.Permissions.Count());
        capturedPermissions.Should().NotBeNull();
        capturedPermissions.Count().Should().Be(request.Permissions.Count());

        _unitOfWorkMock.Verify(x => x.AddRange(It.IsAny<IEnumerable<UserPermission>>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithGroups_ShouldCreateUserWithGroups()
    {
        // Arrange
        var request = CreateValidCreateUserRequest();
        var groupId = request.Groups.First();
        var groups = new List<Group>
        {
            new() { Id = groupId }
        };

        _groupRepositoryMock.Setup(x => x.GetGroups(It.IsAny<HashSet<Guid>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(groups);

        User capturedUser = null;
        _unitOfWorkMock.Setup(x => x.Add(It.IsAny<User>()))
            .Callback<User>(user => capturedUser = user);

        // Act
        await _handler.Handle(request, CancellationToken.None);

        // Assert
        capturedUser.Should().NotBeNull();
        capturedUser.UserGroups.Should().HaveCount(1);
        capturedUser.UserGroups.First().GroupId.Should().Be(groupId);
        capturedUser.UserGroups.First().UserId.Should().Be(request.Id);

        _groupRepositoryMock.Verify(x => x.GetGroups(It.IsAny<HashSet<Guid>>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithPreferences_ShouldCreateUserWithPreferences()
    {
        // Arrange
        var request = CreateValidCreateUserRequest();
        var groups = new List<Group>
        {
            new() { Id = request.Groups.First() }
        };

        _groupRepositoryMock.Setup(x => x.GetGroups(It.IsAny<HashSet<Guid>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(groups);

        User capturedUser = null;
        _unitOfWorkMock.Setup(x => x.Add(It.IsAny<User>()))
            .Callback<User>(user => capturedUser = user);

        // Act
        await _handler.Handle(request, CancellationToken.None);

        // Assert
        capturedUser.Should().NotBeNull();
        capturedUser.Preference.Should().NotBeNull();
        capturedUser.Preference.TimeZone.Should().Be(request.Preference.TimeZone);
        capturedUser.Preference.Language.Should().Be(request.Preference.Language);
        capturedUser.Preference.Theme.Should().Be(request.Preference.Theme);
        capturedUser.Preference.BoldText.Should().Be(request.Preference.BoldText);
    }

    private static CreateUserRequest CreateValidCreateUserRequest()
    {
        return new CreateUserRequest
        {
            Id = Uuid.NewSequential(),
            Name = "Test User",
            Email = "<EMAIL>",
            IsActive = true,
            TenantId = DefaultsValues.BootisId,
            GroupTenantId = DefaultsValues.BootisId,
            Preference = new CreateUserRequest.UserPreference
            {
                Theme = 1,
                BoldText = false,
                ExtendedText = false,
                ContrastIncreased = false,
                TimeZone = "UTC",
                Language = "en-US",
                DateDefault = "dd/MM/yyyy",
                HourDefault = "HH:mm"
            },
            Permissions = new List<CreateUserRequest.Permission>(),
            Groups = new List<Guid> { Uuid.NewSequential() }
        };
    }

    private static CreateUserRequest CreateValidCreateUserRequestWithPermissions()
    {
        var request = CreateValidCreateUserRequest();
        return request with
        {
            Permissions = new List<CreateUserRequest.Permission>
            {
                new(1, true),
                new(2, false)
            }
        };
    }
}