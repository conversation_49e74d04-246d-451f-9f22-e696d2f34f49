using Bootis.Email.Api.Models;
using Bootis.Shared.Common.Extensions;

namespace Bootis.Email.Api.Helper;

public static class EmailHelper
{
    public static EmailLanguageType GetEmailLanguageType()
    {
        try
        {
            var languageType =
                EnumExtensions.GetValueFromDescription<EmailLanguageType>(
                    Thread.CurrentThread.CurrentCulture.ToString());
            return languageType ?? EmailLanguageType.Default;
        }
        catch (Exception)
        {
            return EmailLanguageType.Default;
        }
    }
}