using Bootis.Email.Api.Configuration;
using Bootis.Email.Api.Helper;
using Bootis.Email.Api.Models;
using Bootis.Identity.Contracts;
using Bootis.Shared;
using Bootis.Shared.Common;
using MassTransit;
using MediatR;
using Microsoft.Extensions.Options;

namespace Bootis.Email.Api.Consumers;

public class NewPasswordConsumer(
    IOptions<EmailApiConfiguration> emailApiConfigurationOptions,
    IMediator mediator) : IConsumer<NewPasswordEvent>
{
    private const string NewPasswordTemplate = "NewPassword";

    public async Task Consume(ConsumeContext<NewPasswordEvent> context)
    {
        var config = emailApiConfigurationOptions.Value;
        var user = context.Message;

        var emailAddress = new EmailAddress
        {
            Email = user.Email,
            Name = user.UserName
        };

        var url = $"{config.BaseUrlFront}/new-password/{user.Email}/{user.Id}/{context.Message.Code}";

        var emailLanguageType = EmailHelper.GetEmailLanguageType();

        var properties = new Dictionary<string, string>
        {
            { "UserName", user.UserName },
            { "UrlNewPassword", url },
            { "CreateNewPassord", Localizer.Instance.GetMessage_Email_CriarNovaSenha() },
            { "NewPassord", Localizer.Instance.GetMessage_Email_NovaSenha() }
        };

        var sendEmailCommand = new SendEmail
        {
            Template = NewPasswordTemplate,
            Subject = Localizer.Instance.GetMessage_Email_CriarNovaSenha(),
            Properties = properties,
            From = config.From,
            Tos = [emailAddress],
            AcceptLanguage = emailLanguageType
        };

        await mediator.Send(sendEmailCommand, context.CancellationToken);
    }
}