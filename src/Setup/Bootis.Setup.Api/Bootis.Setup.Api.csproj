<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>disable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <ProjectGuid>{cfbb459b-9666-4775-b324-54e0726969a8}</ProjectGuid>
        <UserSecretsId>b07fd39a-fc17-4fcb-9217-3f5a1bf05c68</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="MassTransit.EntityFrameworkCore" Version="8.5.1"/>
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.7"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.7">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Aspire\Bootis.ServiceDefaults\Bootis.ServiceDefaults.csproj"/>
        <ProjectReference Include="..\..\Shared\Bootis.Shared.Api\Bootis.Shared.Api.csproj"/>
    </ItemGroup>


</Project>

