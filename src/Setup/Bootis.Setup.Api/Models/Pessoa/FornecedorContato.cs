using Bootis.Setup.Api.Attributes;
using UUIDNext;

namespace Bootis.Setup.Api.Models.Pessoa;

[TableName("fornecedor_contatos")]
public record FornecedorContato
{
    public Guid Id { get; set; }
    [IgnoreColumn(Destination = true)] public string FornecedorIdentificacao { get; set; }
    [IgnoreColumn(Source = true)] public Guid FornecedorId { get; set; }
    [IgnoreColumn(Destination = true)] public string TipoContatoNome { get; set; }
    [IgnoreColumn(Source = true)] public Guid TipoContatoId { get; set; }
    public string Identificacao { get; set; }
    public bool Principal { get; set; }
    public string Observacao { get; set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }
}