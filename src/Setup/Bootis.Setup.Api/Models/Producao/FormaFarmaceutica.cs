using Bootis.Setup.Api.Attributes;
using UUIDNext;

namespace Bootis.Setup.Api.Models.Producao;

[TableName("formas_farmaceutica")]
public record class FormaFarmaceutica
{
    public Guid Id { get; set; }
    public string Descricao { get; set; }
    [IgnoreColumn(Destination = true)] public string LaboratorioDescricao { get; set; }
    [IgnoreColumn(Source = true)] public Guid LaboratorioId { get; set; }
    public bool Ativo { get; set; }
    public int Ordem { get; set; }
    public decimal PercentualMinimoExcipiente { get; set; }
    [IgnoreColumn(Destination = true)] public string TipoCalculoDescricao { get; set; }
    [IgnoreColumn(Source = true)] public int TipoCalculo { get; set; }
    [IgnoreColumn(Destination = true)] public string UsoFormaFarmaceuticaDescricao { get; set; }
    [IgnoreColumn(Source = true)] public int UsoFormaFarmaceutica { get; set; }
    [IgnoreColumn(Destination = true)] public string UnidadeMedidaDescricao { get; set; }
    [IgnoreColumn(Source = true)] public int UnidadeMedidaId { get; set; }
    public string Apresentacao { get; set; }
    public int ValidadeDias { get; set; }
    public DateTime DataCadastro { get; set; } = DateTime.UtcNow;
    public decimal CustoOperacional { get; set; }
    public Guid GroupTenantId { get; set; }
    public Guid TenantId { get; set; }
}