using System.Data;
using System.Reflection;
using Bootis.Setup.Api.Attributes;

namespace Bootis.Setup.Api.Extensions;

public static class EnumerableExtensions
{
    public static DataTable ToDataTable<T>(this IEnumerable<T> items)
    {
        var dataTable = new DataTable(typeof(T).Name);
        var props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);

        foreach (var prop in props)
        {
            var columnAlias = prop.GetCustomAttribute<ColumnNameAttribute>()?.Name ?? prop.Name;
            dataTable.Columns.Add(columnAlias, Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType);
        }

        foreach (var item in items)
        {
            var values = props.Select(p => p.GetValue(item, null) ?? DBNull.Value).ToArray();
            dataTable.Rows.Add(values);
        }

        return dataTable;
    }
}