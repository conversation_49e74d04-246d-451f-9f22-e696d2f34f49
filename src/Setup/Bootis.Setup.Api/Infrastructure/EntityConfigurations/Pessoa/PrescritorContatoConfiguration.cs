using Bootis.Setup.Api.RequestHandlers.Pessoa;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Setup.Api.Infrastructure.EntityConfigurations.Pessoa;

public class PrescritorContatoConfiguration : IEntityTypeConfiguration<PrescritorContato>
{
    public void Configure(EntityTypeBuilder<PrescritorContato> builder)
    {
        builder.ToTable("prescritores_contato");

        builder.HasKey(x => x.id);
    }
}