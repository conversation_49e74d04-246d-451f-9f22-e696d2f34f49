using Bootis.Setup.Api.Extensions;
using Bootis.Setup.Api.Models.Estoque;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Setup.Api.Infrastructure.EntityConfigurations.Estoque;

public class LocalEstoqueConfiguration : IEntityTypeConfiguration<LocalEstoque>
{
    public void Configure(EntityTypeBuilder<LocalEstoque> builder)
    {
        builder.ToTable(typeof(LocalEstoque).GetTableName());

        builder.HasKey(x => x.Id);
    }
}