using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bootis.Setup.Api.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class RemoveCapsulaTamanhoAndAjusteFornecedor : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "capsulas_tamanho");

            migrationBuilder.AlterColumn<int>(
                name: "acao_venda_secundaria_id",
                table: "status_atendimento",
                type: "integer",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "acao_venda_primaria_id",
                table: "status_atendimento",
                type: "integer",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "tipo_capsula_id",
                table: "produtos_tipo_capsula",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "produto_id",
                table: "produtos_tipo_capsula",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "numero_capsula_id",
                table: "produtos_tipo_capsula",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "capsula_cor_id",
                table: "produtos_tipo_capsula",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<int>(
                name: "unidade_prescricao_id",
                table: "produtos_materia_prima",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<int>(
                name: "tipo_componente_id",
                table: "produtos_materia_prima",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddColumn<string>(
                name: "observacao_rotulo_armazenagem",
                table: "produtos_materia_prima",
                type: "text",
                nullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "unidade_estoque_id",
                table: "produtos",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<int>(
                name: "classe_produto_id",
                table: "produtos",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<int>(
                name: "unidade_medida_id",
                table: "posologias",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<int>(
                name: "tipo_fornecedor_id",
                table: "fornecedores",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<int>(
                name: "unidade_medida_id",
                table: "formas_farmaceutica",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "observacao_rotulo_armazenagem",
                table: "produtos_materia_prima");

            migrationBuilder.AlterColumn<Guid>(
                name: "acao_venda_secundaria_id",
                table: "status_atendimento",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer",
                oldNullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "acao_venda_primaria_id",
                table: "status_atendimento",
                type: "uuid",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "integer",
                oldNullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "tipo_capsula_id",
                table: "produtos_tipo_capsula",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<int>(
                name: "produto_id",
                table: "produtos_tipo_capsula",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<int>(
                name: "numero_capsula_id",
                table: "produtos_tipo_capsula",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<int>(
                name: "capsula_cor_id",
                table: "produtos_tipo_capsula",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<Guid>(
                name: "unidade_prescricao_id",
                table: "produtos_materia_prima",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "tipo_componente_id",
                table: "produtos_materia_prima",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "unidade_estoque_id",
                table: "produtos",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "classe_produto_id",
                table: "produtos",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "unidade_medida_id",
                table: "posologias",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "tipo_fornecedor_id",
                table: "fornecedores",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "unidade_medida_id",
                table: "formas_farmaceutica",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.CreateTable(
                name: "capsulas_tamanho",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uuid", nullable: false),
                    group_tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    numero_capsula = table.Column<string>(type: "text", nullable: true),
                    tenant_id = table.Column<Guid>(type: "uuid", nullable: false),
                    volume_ml = table.Column<decimal>(type: "numeric", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_capsulas_tamanho", x => x.id);
                });
        }
    }
}
