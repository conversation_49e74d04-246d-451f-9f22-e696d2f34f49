// <auto-generated />
using System;
using Bootis.Setup.Api.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Bootis.Setup.Api.Infrastructure.Migrations
{
    [DbContext(typeof(SetupContext))]
    partial class SetupContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Bootis.Setup.Api.Models.AccountManager.Grupo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("Nome")
                        .HasColumnType("text")
                        .HasColumnName("nome");

                    b.Property<bool>("SeAtivo")
                        .HasColumnType("boolean")
                        .HasColumnName("se_ativo");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_grupos");

                    b.ToTable("grupos", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.AccountManager.GrupoPermissao", b =>
                {
                    b.Property<Guid>("GrupoId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("grupo_id");

                    b.Property<int>("PermissaoId")
                        .HasColumnType("integer")
                        .HasColumnName("permissao_id");

                    b.HasKey("GrupoId")
                        .HasName("pk_grupos_permissoes");

                    b.ToTable("grupos_permissoes", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Estoque.Grupo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_produto_grupos");

                    b.ToTable("produto_grupos", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Estoque.LocalEstoque", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid")
                        .HasColumnName("empresa_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<int>("SequenciaGroupTenant")
                        .HasColumnType("integer")
                        .HasColumnName("sequencia_group_tenant");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TipoEstoque")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_estoque");

                    b.HasKey("Id")
                        .HasName("pk_locais_estoque");

                    b.ToTable("locais_estoque", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Estoque.Produto", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.Property<string>("ClasseProdutoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("classe_produto_descricao");

                    b.Property<int>("ClasseProdutoId")
                        .HasColumnType("integer")
                        .HasColumnName("classe_produto_id");

                    b.Property<bool>("ControlaLote")
                        .HasColumnType("boolean")
                        .HasColumnName("controla_lote");

                    b.Property<bool>("ControlaQualidade")
                        .HasColumnType("boolean")
                        .HasColumnName("controla_qualidade");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<string>("DescricaoRotulo")
                        .HasColumnType("text")
                        .HasColumnName("descricao_rotulo");

                    b.Property<bool>("Etiqueta")
                        .HasColumnType("boolean")
                        .HasColumnName("etiqueta");

                    b.Property<Guid?>("FornecedorId")
                        .HasColumnType("uuid")
                        .HasColumnName("fornecedor_id");

                    b.Property<string>("FornecedorIdentificacao")
                        .HasColumnType("text")
                        .HasColumnName("fornecedor_identificacao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<decimal>("MargemLucro")
                        .HasColumnType("numeric")
                        .HasColumnName("margem_lucro");

                    b.Property<int>("SequenciaGroupTenant")
                        .HasColumnType("integer")
                        .HasColumnName("sequencia_group_tenant");

                    b.Property<string>("SubGrupoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("sub_grupo_descricao");

                    b.Property<Guid>("SubGrupoId")
                        .HasColumnType("uuid")
                        .HasColumnName("sub_grupo_id");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("UnidadeEstoqueDescricao")
                        .HasColumnType("text")
                        .HasColumnName("unidade_estoque_descricao");

                    b.Property<int>("UnidadeEstoqueId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_estoque_id");

                    b.Property<bool>("UsoContinuo")
                        .HasColumnType("boolean")
                        .HasColumnName("uso_continuo");

                    b.Property<decimal>("ValorCusto")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_custo");

                    b.Property<decimal>("ValorVenda")
                        .HasColumnType("numeric")
                        .HasColumnName("valor_venda");

                    b.HasKey("Id")
                        .HasName("pk_produtos");

                    b.ToTable("produtos", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Estoque.ProdutoMateriaPrima", b =>
                {
                    b.Property<string>("ProdutoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("produto_descricao");

                    b.Property<Guid?>("CasId")
                        .HasColumnType("uuid")
                        .HasColumnName("cas_id");

                    b.Property<Guid?>("DcbId")
                        .HasColumnType("uuid")
                        .HasColumnName("dcb_id");

                    b.Property<int?>("DiasValidade")
                        .HasColumnType("integer")
                        .HasColumnName("dias_validade");

                    b.Property<bool?>("ExigeCapsulaGastroresistente")
                        .HasColumnType("boolean")
                        .HasColumnName("exige_capsula_gastroresistente");

                    b.Property<decimal>("FatorEquivalencia")
                        .HasColumnType("numeric")
                        .HasColumnName("fator_equivalencia");

                    b.Property<bool?>("IsExcipiente")
                        .HasColumnType("boolean")
                        .HasColumnName("is_excipiente");

                    b.Property<bool?>("IsMonodroga")
                        .HasColumnType("boolean")
                        .HasColumnName("is_monodroga");

                    b.Property<bool?>("IsPellets")
                        .HasColumnType("boolean")
                        .HasColumnName("is_pellets");

                    b.Property<bool?>("IsQsp")
                        .HasColumnType("boolean")
                        .HasColumnName("is_qsp");

                    b.Property<string>("NumeroCas")
                        .HasColumnType("text")
                        .HasColumnName("numero_cas");

                    b.Property<int?>("NumeroDcb")
                        .HasColumnType("integer")
                        .HasColumnName("numero_dcb");

                    b.Property<string>("ObservacaoRotuloArmazenagem")
                        .HasColumnType("text")
                        .HasColumnName("observacao_rotulo_armazenagem");

                    b.Property<decimal>("PesoMolecularBase")
                        .HasColumnType("numeric")
                        .HasColumnName("peso_molecular_base");

                    b.Property<decimal>("PesoMolecularSal")
                        .HasColumnType("numeric")
                        .HasColumnName("peso_molecular_sal");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<bool?>("SomenteDiluido")
                        .HasColumnType("boolean")
                        .HasColumnName("somente_diluido");

                    b.Property<bool?>("SomenteLaboratorio")
                        .HasColumnType("boolean")
                        .HasColumnName("somente_laboratorio");

                    b.Property<string>("TipoComponenteDescricao")
                        .HasColumnType("text")
                        .HasColumnName("tipo_componente_descricao");

                    b.Property<int>("TipoComponenteId")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_componente_id");

                    b.Property<decimal?>("ToleranciaPesagemDown")
                        .HasColumnType("numeric")
                        .HasColumnName("tolerancia_pesagem_down");

                    b.Property<decimal?>("ToleranciaPesagemUp")
                        .HasColumnType("numeric")
                        .HasColumnName("tolerancia_pesagem_up");

                    b.Property<string>("UnidadePrescricaoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("unidade_prescricao_descricao");

                    b.Property<int>("UnidadePrescricaoId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_prescricao_id");

                    b.Property<decimal?>("Valencia")
                        .HasColumnType("numeric")
                        .HasColumnName("valencia");

                    b.HasKey("ProdutoDescricao")
                        .HasName("pk_produtos_materia_prima");

                    b.ToTable("produtos_materia_prima", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Estoque.ProdutoTipoCapsula", b =>
                {
                    b.Property<string>("ProdutoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("produto_descricao");

                    b.Property<string>("CapsulaCorDescricao")
                        .HasColumnType("text")
                        .HasColumnName("capsula_cor_descricao");

                    b.Property<Guid>("CapsulaCorId")
                        .HasColumnType("uuid")
                        .HasColumnName("capsula_cor_id");

                    b.Property<Guid>("NumeroCapsulaId")
                        .HasColumnType("uuid")
                        .HasColumnName("numero_capsula_id");

                    b.Property<string>("NumeroCapsulaTamanho")
                        .HasColumnType("text")
                        .HasColumnName("numero_capsula_tamanho");

                    b.Property<Guid>("ProdutoId")
                        .HasColumnType("uuid")
                        .HasColumnName("produto_id");

                    b.Property<string>("TipoCapsulaDescricao")
                        .HasColumnType("text")
                        .HasColumnName("tipo_capsula_descricao");

                    b.Property<Guid>("TipoCapsulaId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_capsula_id");

                    b.HasKey("ProdutoDescricao")
                        .HasName("pk_produtos_tipo_capsula");

                    b.ToTable("produtos_tipo_capsula", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Estoque.SubGrupo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("GrupoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("grupo_descricao");

                    b.Property<Guid>("GrupoId")
                        .HasColumnType("uuid")
                        .HasColumnName("grupo_id");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_sub_grupos");

                    b.ToTable("sub_grupos", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Pessoa.EspecialidadePrescritor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_especialidades_prescritor");

                    b.ToTable("especialidades_prescritor", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Pessoa.Fornecedor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.Property<string>("Cnpj")
                        .HasColumnType("text")
                        .HasColumnName("cnpj");

                    b.Property<string>("Cpf")
                        .HasColumnType("text")
                        .HasColumnName("cpf");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("Nome")
                        .HasColumnType("text")
                        .HasColumnName("nome");

                    b.Property<string>("Observacao")
                        .HasColumnType("text")
                        .HasColumnName("observacao");

                    b.Property<string>("RazaoSocial")
                        .HasColumnType("text")
                        .HasColumnName("razao_social");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TipoFornecedorId")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_fornecedor_id");

                    b.Property<int>("TipoPessoa")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_pessoa");

                    b.HasKey("Id")
                        .HasName("pk_fornecedores");

                    b.ToTable("fornecedores", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Pessoa.FornecedorContato", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("FornecedorId")
                        .HasColumnType("uuid")
                        .HasColumnName("fornecedor_id");

                    b.Property<string>("FornecedorIdentificacao")
                        .HasColumnType("text")
                        .HasColumnName("fornecedor_identificacao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("Identificacao")
                        .HasColumnType("text")
                        .HasColumnName("identificacao");

                    b.Property<string>("Observacao")
                        .HasColumnType("text")
                        .HasColumnName("observacao");

                    b.Property<bool>("Principal")
                        .HasColumnType("boolean")
                        .HasColumnName("principal");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("TipoContatoId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_contato_id");

                    b.Property<string>("TipoContatoNome")
                        .HasColumnType("text")
                        .HasColumnName("tipo_contato_nome");

                    b.HasKey("Id")
                        .HasName("pk_fornecedor_contatos");

                    b.ToTable("fornecedor_contatos", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Pessoa.FornecedorDocumento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<Guid>("FornecedorId")
                        .HasColumnType("uuid")
                        .HasColumnName("fornecedor_id");

                    b.Property<string>("FornecedorIdentificacao")
                        .HasColumnType("text")
                        .HasColumnName("fornecedor_identificacao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("Identificacao")
                        .HasColumnType("text")
                        .HasColumnName("identificacao");

                    b.Property<string>("Observacao")
                        .HasColumnType("text")
                        .HasColumnName("observacao");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("TipoDocumentoId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_documento_id");

                    b.Property<string>("TipoDocumentoNome")
                        .HasColumnType("text")
                        .HasColumnName("tipo_documento_nome");

                    b.HasKey("Id")
                        .HasName("pk_fornecedor_documentos");

                    b.ToTable("fornecedor_documentos", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Pessoa.FornecedorEndereco", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Bairro")
                        .HasColumnType("text")
                        .HasColumnName("bairro");

                    b.Property<string>("Cep")
                        .HasColumnType("text")
                        .HasColumnName("cep");

                    b.Property<string>("CidadeDescricao")
                        .HasColumnType("text")
                        .HasColumnName("cidade_descricao");

                    b.Property<Guid?>("CidadeId")
                        .HasColumnType("uuid")
                        .HasColumnName("cidade_id");

                    b.Property<string>("Complemento")
                        .HasColumnType("text")
                        .HasColumnName("complemento");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<string>("EstadoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("estado_descricao");

                    b.Property<Guid>("EstadoId")
                        .HasColumnType("uuid")
                        .HasColumnName("estado_id");

                    b.Property<Guid>("FornecedorId")
                        .HasColumnType("uuid")
                        .HasColumnName("fornecedor_id");

                    b.Property<string>("FornecedorIdentificacao")
                        .HasColumnType("text")
                        .HasColumnName("fornecedor_identificacao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("Logradouro")
                        .HasColumnType("text")
                        .HasColumnName("logradouro");

                    b.Property<string>("Numero")
                        .HasColumnType("text")
                        .HasColumnName("numero");

                    b.Property<string>("PaisDescricao")
                        .HasColumnType("text")
                        .HasColumnName("pais_descricao");

                    b.Property<Guid>("PaisId")
                        .HasColumnType("uuid")
                        .HasColumnName("pais_id");

                    b.Property<bool>("Principal")
                        .HasColumnType("boolean")
                        .HasColumnName("principal");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_fornecedor_enderecos");

                    b.ToTable("fornecedor_enderecos", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Producao.CapsulaCor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("CorCapsula")
                        .HasColumnType("text")
                        .HasColumnName("cor_capsula");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<bool>("Transparente")
                        .HasColumnType("boolean")
                        .HasColumnName("transparente");

                    b.HasKey("Id")
                        .HasName("pk_capsulas_cor");

                    b.ToTable("capsulas_cor", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Producao.EmbalagemClassificacao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_embalagens_classificacao");

                    b.ToTable("embalagens_classificacao", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Producao.FormaFarmaceutica", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Apresentacao")
                        .HasColumnType("text")
                        .HasColumnName("apresentacao");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.Property<decimal>("CustoOperacional")
                        .HasColumnType("numeric")
                        .HasColumnName("custo_operacional");

                    b.Property<DateTime>("DataCadastro")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_cadastro");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("LaboratorioDescricao")
                        .HasColumnType("text")
                        .HasColumnName("laboratorio_descricao");

                    b.Property<Guid>("LaboratorioId")
                        .HasColumnType("uuid")
                        .HasColumnName("laboratorio_id");

                    b.Property<int>("Ordem")
                        .HasColumnType("integer")
                        .HasColumnName("ordem");

                    b.Property<decimal>("PercentualMinimoExcipiente")
                        .HasColumnType("numeric")
                        .HasColumnName("percentual_minimo_excipiente");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<int>("TipoCalculo")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_calculo");

                    b.Property<string>("TipoCalculoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("tipo_calculo_descricao");

                    b.Property<string>("UnidadeMedidaDescricao")
                        .HasColumnType("text")
                        .HasColumnName("unidade_medida_descricao");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.Property<int>("UsoFormaFarmaceutica")
                        .HasColumnType("integer")
                        .HasColumnName("uso_forma_farmaceutica");

                    b.Property<string>("UsoFormaFarmaceuticaDescricao")
                        .HasColumnType("text")
                        .HasColumnName("uso_forma_farmaceutica_descricao");

                    b.Property<int>("ValidadeDias")
                        .HasColumnType("integer")
                        .HasColumnName("validade_dias");

                    b.HasKey("Id")
                        .HasName("pk_formas_farmaceutica");

                    b.ToTable("formas_farmaceutica", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Producao.Laboratorio", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.Property<string>("EmpresaDescricao")
                        .HasColumnType("text")
                        .HasColumnName("empresa_descricao");

                    b.Property<Guid>("EmpresaId")
                        .HasColumnType("uuid")
                        .HasColumnName("empresa_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<string>("LocalEstoqueDescricao")
                        .HasColumnType("text")
                        .HasColumnName("local_estoque_descricao");

                    b.Property<Guid>("LocalEstoqueId")
                        .HasColumnType("uuid")
                        .HasColumnName("local_estoque_id");

                    b.Property<string>("NomeLaboratorio")
                        .HasColumnType("text")
                        .HasColumnName("nome_laboratorio");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.HasKey("Id")
                        .HasName("pk_laboratorios");

                    b.ToTable("laboratorios", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Producao.Posologia", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<string>("FormaFarmaceuticaDescricao")
                        .HasColumnType("text")
                        .HasColumnName("forma_farmaceutica_descricao");

                    b.Property<Guid>("FormaFarmaceuticaId")
                        .HasColumnType("uuid")
                        .HasColumnName("forma_farmaceutica_id");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<int>("Periodo")
                        .HasColumnType("integer")
                        .HasColumnName("periodo");

                    b.Property<string>("PeriodoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("periodo_descricao");

                    b.Property<decimal>("QuantidadeDosePorPeriodo")
                        .HasColumnType("numeric")
                        .HasColumnName("quantidade_dose_por_periodo");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<string>("UnidadeMedidaDescricao")
                        .HasColumnType("text")
                        .HasColumnName("unidade_medida_descricao");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.HasKey("Id")
                        .HasName("pk_posologias");

                    b.ToTable("posologias", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Models.Vendas.StatusAtendimento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("AcaoVendaPrimariaDescricao")
                        .HasColumnType("text")
                        .HasColumnName("acao_venda_primaria_descricao");

                    b.Property<int?>("AcaoVendaPrimariaId")
                        .HasColumnType("integer")
                        .HasColumnName("acao_venda_primaria_id");

                    b.Property<string>("AcaoVendaSecundariaDescricao")
                        .HasColumnType("text")
                        .HasColumnName("acao_venda_secundaria_descricao");

                    b.Property<int?>("AcaoVendaSecundariaId")
                        .HasColumnType("integer")
                        .HasColumnName("acao_venda_secundaria_id");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<int>("Ordem")
                        .HasColumnType("integer")
                        .HasColumnName("ordem");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<Guid>("UsuarioId")
                        .HasColumnType("uuid")
                        .HasColumnName("usuario_id");

                    b.HasKey("Id")
                        .HasName("pk_status_atendimento");

                    b.ToTable("status_atendimento", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Estoque.Lote", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<DateTime>("DataFabricacao")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_fabricacao");

                    b.Property<DateTime>("DataLancamento")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_lancamento");

                    b.Property<DateTime>("DataValidade")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_validade");

                    b.Property<string>("FornecedorIdentificacao")
                        .HasColumnType("text")
                        .HasColumnName("fornecedor_identificacao");

                    b.Property<string>("Numero")
                        .HasColumnType("text")
                        .HasColumnName("numero");

                    b.Property<int>("NumeroNf")
                        .HasColumnType("integer")
                        .HasColumnName("numero_nf");

                    b.Property<string>("ProdutoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("produto_descricao");

                    b.Property<int>("SerieNf")
                        .HasColumnType("integer")
                        .HasColumnName("serie_nf");

                    b.Property<int>("Situacao")
                        .HasColumnType("integer")
                        .HasColumnName("situacao");

                    b.HasKey("Id")
                        .HasName("pk_lotes");

                    b.ToTable("lotes", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Estoque.LoteInformacaoTecnica", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<decimal?>("ConcentracaoAgua")
                        .HasColumnType("numeric")
                        .HasColumnName("concentracao_agua");

                    b.Property<decimal>("Densidade")
                        .HasColumnType("numeric")
                        .HasColumnName("densidade");

                    b.Property<decimal>("DiluicaoFornecedor")
                        .HasColumnType("numeric")
                        .HasColumnName("diluicao_fornecedor");

                    b.Property<decimal?>("DiluicaoInterna")
                        .HasColumnType("numeric")
                        .HasColumnName("diluicao_interna");

                    b.Property<decimal?>("FatorConcentracaoAgua")
                        .HasColumnType("numeric")
                        .HasColumnName("fator_concentracao_agua");

                    b.Property<decimal>("FatorDiluicaoFornecedor")
                        .HasColumnType("numeric")
                        .HasColumnName("fator_diluicao_fornecedor");

                    b.Property<decimal?>("FatorDiluicaoInterna")
                        .HasColumnType("numeric")
                        .HasColumnName("fator_diluicao_interna");

                    b.Property<string>("LoteNumero")
                        .HasColumnType("text")
                        .HasColumnName("lote_numero");

                    b.Property<int>("LoteNumeroNf")
                        .HasColumnType("integer")
                        .HasColumnName("lote_numero_nf");

                    b.Property<string>("LoteOrigemNumero")
                        .HasColumnType("text")
                        .HasColumnName("lote_origem_numero");

                    b.Property<int>("LoteOrigemNumeroNf")
                        .HasColumnType("integer")
                        .HasColumnName("lote_origem_numero_nf");

                    b.Property<string>("LoteOrigemProdutoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("lote_origem_produto_descricao");

                    b.Property<int>("LoteOrigemSerieNf")
                        .HasColumnType("integer")
                        .HasColumnName("lote_origem_serie_nf");

                    b.Property<string>("LoteProdutoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("lote_produto_descricao");

                    b.Property<int>("LoteSerieNf")
                        .HasColumnType("integer")
                        .HasColumnName("lote_serie_nf");

                    b.Property<string>("PaisDescricao")
                        .HasColumnType("text")
                        .HasColumnName("pais_descricao");

                    b.HasKey("Id")
                        .HasName("pk_lotes_informacao_tecnica");

                    b.ToTable("lotes_informacao_tecnica", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Estoque.Mensagem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<bool>("ExibeFichaPesagem")
                        .HasColumnType("boolean")
                        .HasColumnName("exibe_ficha_pesagem");

                    b.Property<bool>("ExibeImpressaoRotulo")
                        .HasColumnType("boolean")
                        .HasColumnName("exibe_impressao_rotulo");

                    b.Property<bool>("ExibeRotulagem")
                        .HasColumnType("boolean")
                        .HasColumnName("exibe_rotulagem");

                    b.Property<bool>("ExibeVenda")
                        .HasColumnType("boolean")
                        .HasColumnName("exibe_venda");

                    b.HasKey("Id")
                        .HasName("pk_mensagens");

                    b.ToTable("mensagens", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Estoque.ProdutoAssociado", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Acumula")
                        .HasColumnType("boolean")
                        .HasColumnName("acumula");

                    b.Property<decimal>("DosagemMaxima")
                        .HasColumnType("numeric")
                        .HasColumnName("dosagem_maxima");

                    b.Property<decimal>("DosagemMinima")
                        .HasColumnType("numeric")
                        .HasColumnName("dosagem_minima");

                    b.Property<string>("FormaFarmaceuticaDescricao")
                        .HasColumnType("text")
                        .HasColumnName("forma_farmaceutica_descricao");

                    b.Property<string>("ProdutoAssociadoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("produto_associado_descricao");

                    b.Property<string>("ProdutoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("produto_descricao");

                    b.Property<decimal>("QuantidadeAssociada")
                        .HasColumnType("numeric")
                        .HasColumnName("quantidade_associada");

                    b.Property<int>("TipoRelacaoQuantidade")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_relacao_quantidade");

                    b.Property<int>("UnidadeMedidaDosagem")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_dosagem");

                    b.Property<int>("UnidadeMedidaQuantidadeAssociada")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_quantidade_associada");

                    b.HasKey("Id")
                        .HasName("pk_produtos_associado");

                    b.ToTable("produtos_associado", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Estoque.ProdutoDiluido", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<decimal>("Diluicao")
                        .HasColumnType("numeric")
                        .HasColumnName("diluicao");

                    b.Property<decimal>("DosagemMaxima")
                        .HasColumnType("numeric")
                        .HasColumnName("dosagem_maxima");

                    b.Property<decimal>("DosagemMinima")
                        .HasColumnType("numeric")
                        .HasColumnName("dosagem_minima");

                    b.Property<string>("FormaFarmaceuticaDescricao")
                        .HasColumnType("text")
                        .HasColumnName("forma_farmaceutica_descricao");

                    b.Property<string>("ProdutoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("produto_descricao");

                    b.Property<bool>("SeQualquerDosagem")
                        .HasColumnType("boolean")
                        .HasColumnName("se_qualquer_dosagem");

                    b.Property<bool>("SeTodasFormasFarmaceuticas")
                        .HasColumnType("boolean")
                        .HasColumnName("se_todas_formas_farmaceuticas");

                    b.Property<int>("UnidadeMedidaId")
                        .HasColumnType("integer")
                        .HasColumnName("unidade_medida_id");

                    b.HasKey("Id")
                        .HasName("pk_produtos_diluido");

                    b.ToTable("produtos_diluido", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Estoque.ProdutoEmbalagem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("EmbalagemClassificacaoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("embalagem_classificacao_descricao");

                    b.Property<string>("ProdutoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("produto_descricao");

                    b.Property<decimal>("Volume")
                        .HasColumnType("numeric")
                        .HasColumnName("volume");

                    b.HasKey("Id")
                        .HasName("pk_produtos_embalagem");

                    b.ToTable("produtos_embalagem", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Estoque.ProdutoIncompativel", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<int>("NivelIncompatibilidade")
                        .HasColumnType("integer")
                        .HasColumnName("nivel_incompatibilidade");

                    b.Property<string>("ProdutoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("produto_descricao");

                    b.Property<string>("ProdutoIncompativelDescricao")
                        .HasColumnType("text")
                        .HasColumnName("produto_incompativel_descricao");

                    b.HasKey("Id")
                        .HasName("pk_produtos_incompativel");

                    b.ToTable("produtos_incompativel", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Estoque.ProdutoSinonimo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("DescricaoRotulo")
                        .HasColumnType("text")
                        .HasColumnName("descricao_rotulo");

                    b.Property<decimal?>("FatorEquivalencia")
                        .HasColumnType("numeric")
                        .HasColumnName("fator_equivalencia");

                    b.Property<decimal?>("PercentualCorrecao")
                        .HasColumnType("numeric")
                        .HasColumnName("percentual_correcao");

                    b.Property<string>("ProdutoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("produto_descricao");

                    b.Property<string>("Sinonimo")
                        .HasColumnType("text")
                        .HasColumnName("sinonimo");

                    b.HasKey("Id")
                        .HasName("pk_produtos_sinonimo");

                    b.ToTable("produtos_sinonimo", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Pessoa.Cliente", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("Ativo")
                        .HasColumnType("boolean")
                        .HasColumnName("ativo");

                    b.Property<string>("Cnpj")
                        .HasColumnType("text")
                        .HasColumnName("cnpj");

                    b.Property<string>("Cpf")
                        .HasColumnType("text")
                        .HasColumnName("cpf");

                    b.Property<DateTime?>("DataNascimento")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_nascimento");

                    b.Property<decimal?>("DescontoFormulas")
                        .HasColumnType("numeric")
                        .HasColumnName("desconto_formulas");

                    b.Property<decimal?>("DescontoProdutosAcabados")
                        .HasColumnType("numeric")
                        .HasColumnName("desconto_produtos_acabados");

                    b.Property<string>("Nome")
                        .HasColumnType("text")
                        .HasColumnName("nome");

                    b.Property<string>("Observacao")
                        .HasColumnType("text")
                        .HasColumnName("observacao");

                    b.Property<int>("Pessoa")
                        .HasColumnType("integer")
                        .HasColumnName("pessoa");

                    b.Property<string>("RazaoSocial")
                        .HasColumnType("text")
                        .HasColumnName("razao_social");

                    b.Property<int>("SequenciaGroupTenant")
                        .HasColumnType("integer")
                        .HasColumnName("sequencia_group_tenant");

                    b.HasKey("Id")
                        .HasName("pk_clientes");

                    b.ToTable("clientes", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Pessoa.ClienteContato", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ClienteIdentificacao")
                        .HasColumnType("text")
                        .HasColumnName("cliente_identificacao");

                    b.Property<string>("Identificacao")
                        .HasColumnType("text")
                        .HasColumnName("identificacao");

                    b.Property<string>("Observacao")
                        .HasColumnType("text")
                        .HasColumnName("observacao");

                    b.Property<bool>("Principal")
                        .HasColumnType("boolean")
                        .HasColumnName("principal");

                    b.Property<Guid>("TipoContatoId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_contato_id");

                    b.HasKey("Id")
                        .HasName("pk_clientes_contato");

                    b.ToTable("clientes_contato", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Pessoa.ClienteDocumento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("ClienteIdentificacao")
                        .HasColumnType("text")
                        .HasColumnName("cliente_identificacao");

                    b.Property<string>("Identificacao")
                        .HasColumnType("text")
                        .HasColumnName("identificacao");

                    b.Property<string>("Observacao")
                        .HasColumnType("text")
                        .HasColumnName("observacao");

                    b.Property<Guid>("TipoDocumentoId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_documento_id");

                    b.HasKey("Id")
                        .HasName("pk_clientes_documento");

                    b.ToTable("clientes_documento", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Pessoa.ClienteEndereco", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Bairro")
                        .HasColumnType("text")
                        .HasColumnName("bairro");

                    b.Property<string>("Cep")
                        .HasColumnType("text")
                        .HasColumnName("cep");

                    b.Property<string>("CidadeDescricao")
                        .HasColumnType("text")
                        .HasColumnName("cidade_descricao");

                    b.Property<string>("ClienteIdentificacao")
                        .HasColumnType("text")
                        .HasColumnName("cliente_identificacao");

                    b.Property<string>("Complemento")
                        .HasColumnType("text")
                        .HasColumnName("complemento");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<string>("EstadoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("estado_descricao");

                    b.Property<string>("Logradouro")
                        .HasColumnType("text")
                        .HasColumnName("logradouro");

                    b.Property<string>("Numero")
                        .HasColumnType("text")
                        .HasColumnName("numero");

                    b.Property<string>("PaisDescricao")
                        .HasColumnType("text")
                        .HasColumnName("pais_descricao");

                    b.Property<bool>("Principal")
                        .HasColumnType("boolean")
                        .HasColumnName("principal");

                    b.HasKey("Id")
                        .HasName("pk_clientes_endereco");

                    b.ToTable("clientes_endereco", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Pessoa.Prescritor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("CodigoRegistro")
                        .HasColumnType("text")
                        .HasColumnName("codigo_registro");

                    b.Property<DateTime?>("DataNascimento")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("data_nascimento");

                    b.Property<decimal?>("DescontoFormulas")
                        .HasColumnType("numeric")
                        .HasColumnName("desconto_formulas");

                    b.Property<decimal?>("DescontoProdutosAcabados")
                        .HasColumnType("numeric")
                        .HasColumnName("desconto_produtos_acabados");

                    b.Property<string>("NomeCompleto")
                        .HasColumnType("text")
                        .HasColumnName("nome_completo");

                    b.Property<string>("Observacao")
                        .HasColumnType("text")
                        .HasColumnName("observacao");

                    b.Property<string>("TipoRegistroSigla")
                        .HasColumnType("text")
                        .HasColumnName("tipo_registro_sigla");

                    b.Property<string>("UfRegistroDescricao")
                        .HasColumnType("text")
                        .HasColumnName("uf_registro_descricao");

                    b.HasKey("Id")
                        .HasName("pk_prescritores");

                    b.ToTable("prescritores", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Pessoa.PrescritorContato", b =>
                {
                    b.Property<Guid>("id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Identificacao")
                        .HasColumnType("text")
                        .HasColumnName("identificacao");

                    b.Property<string>("Observacao")
                        .HasColumnType("text")
                        .HasColumnName("observacao");

                    b.Property<string>("PrescritorNomeCompleto")
                        .HasColumnType("text")
                        .HasColumnName("prescritor_nome_completo");

                    b.Property<bool>("Principal")
                        .HasColumnType("boolean")
                        .HasColumnName("principal");

                    b.Property<Guid>("TipoContatoId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_contato_id");

                    b.HasKey("id")
                        .HasName("pk_prescritores_contato");

                    b.ToTable("prescritores_contato", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Pessoa.PrescritorDocumento", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Identificacao")
                        .HasColumnType("text")
                        .HasColumnName("identificacao");

                    b.Property<string>("Observacao")
                        .HasColumnType("text")
                        .HasColumnName("observacao");

                    b.Property<string>("PrescritorNomeCompleto")
                        .HasColumnType("text")
                        .HasColumnName("prescritor_nome_completo");

                    b.Property<Guid>("TipoDocumentoId")
                        .HasColumnType("uuid")
                        .HasColumnName("tipo_documento_id");

                    b.HasKey("Id")
                        .HasName("pk_prescritores_documento");

                    b.ToTable("prescritores_documento", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Pessoa.PrescritorEndereco", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<string>("Bairro")
                        .HasColumnType("text")
                        .HasColumnName("bairro");

                    b.Property<string>("Cep")
                        .HasColumnType("text")
                        .HasColumnName("cep");

                    b.Property<string>("CidadeDescricao")
                        .HasColumnType("text")
                        .HasColumnName("cidade_descricao");

                    b.Property<string>("Complemento")
                        .HasColumnType("text")
                        .HasColumnName("complemento");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.Property<string>("EstadoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("estado_descricao");

                    b.Property<string>("Logradouro")
                        .HasColumnType("text")
                        .HasColumnName("logradouro");

                    b.Property<string>("Numero")
                        .HasColumnType("text")
                        .HasColumnName("numero");

                    b.Property<string>("PaisDescricao")
                        .HasColumnType("text")
                        .HasColumnName("pais_descricao");

                    b.Property<string>("PrescritorNomeCompleto")
                        .HasColumnType("text")
                        .HasColumnName("prescritor_nome_completo");

                    b.Property<bool>("Principal")
                        .HasColumnType("boolean")
                        .HasColumnName("principal");

                    b.HasKey("Id")
                        .HasName("pk_prescritores_endereco");

                    b.ToTable("prescritores_endereco", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Producao.FormulaPadrao", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<decimal>("Densidade")
                        .HasColumnType("numeric")
                        .HasColumnName("densidade");

                    b.Property<int>("DiasValidade")
                        .HasColumnType("integer")
                        .HasColumnName("dias_validade");

                    b.Property<decimal>("Diluicao")
                        .HasColumnType("numeric")
                        .HasColumnName("diluicao");

                    b.Property<string>("FormaFarmaceuticaDescricao")
                        .HasColumnType("text")
                        .HasColumnName("forma_farmaceutica_descricao");

                    b.Property<int>("FormulaPadraoDesmembramento")
                        .HasColumnType("integer")
                        .HasColumnName("formula_padrao_desmembramento");

                    b.Property<string>("Procedimento")
                        .HasColumnType("text")
                        .HasColumnName("procedimento");

                    b.Property<string>("ProdutoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("produto_descricao");

                    b.Property<decimal>("QuantidadePadrao")
                        .HasColumnType("numeric")
                        .HasColumnName("quantidade_padrao");

                    b.Property<string>("Rodape")
                        .HasColumnType("text")
                        .HasColumnName("rodape");

                    b.Property<Guid>("UnidadeMedidaId")
                        .HasColumnType("uuid")
                        .HasColumnName("unidade_medida_id");

                    b.HasKey("Id")
                        .HasName("pk_formulas_padrao");

                    b.ToTable("formulas_padrao", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Producao.FormulaPadraoItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<int>("Fase")
                        .HasColumnType("integer")
                        .HasColumnName("fase");

                    b.Property<string>("FormulaPadraoProdutoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("formula_padrao_produto_descricao");

                    b.Property<string>("ProdutoDescricao")
                        .HasColumnType("text")
                        .HasColumnName("produto_descricao");

                    b.Property<decimal>("Quantidade")
                        .HasColumnType("numeric")
                        .HasColumnName("quantidade");

                    b.Property<int>("TipoItem")
                        .HasColumnType("integer")
                        .HasColumnName("tipo_item");

                    b.Property<Guid>("UnidadeMedidaId")
                        .HasColumnType("uuid")
                        .HasColumnName("unidade_medida_id");

                    b.HasKey("Id")
                        .HasName("pk_formulas_padrao_item");

                    b.ToTable("formulas_padrao_item", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.RequestHandlers.Producao.TipoCapsula", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid")
                        .HasColumnName("id");

                    b.Property<bool>("CapsulaGastroresistente")
                        .HasColumnType("boolean")
                        .HasColumnName("capsula_gastroresistente");

                    b.Property<bool>("CapsulaVegetal")
                        .HasColumnType("boolean")
                        .HasColumnName("capsula_vegetal");

                    b.Property<string>("Descricao")
                        .HasColumnType("text")
                        .HasColumnName("descricao");

                    b.HasKey("Id")
                        .HasName("pk_tipos_capsula");

                    b.ToTable("tipos_capsula", (string)null);
                });

            modelBuilder.Entity("Bootis.Setup.Api.Saga.DataMigrationState", b =>
                {
                    b.Property<Guid>("CorrelationId")
                        .HasColumnType("uuid")
                        .HasColumnName("correlation_id");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uuid")
                        .HasColumnName("company_id");

                    b.Property<DateTime>("Created")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("created");

                    b.Property<string>("CurrentState")
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)")
                        .HasColumnName("current_state");

                    b.Property<string>("FailedStep")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("failed_step");

                    b.Property<Guid>("GroupTenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("group_tenant_id");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer")
                        .HasColumnName("retry_count");

                    b.Property<string>("SourceConnectionString")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)")
                        .HasColumnName("source_connection_string");

                    b.Property<Guid>("TenantId")
                        .HasColumnType("uuid")
                        .HasColumnName("tenant_id");

                    b.Property<DateTime?>("Updated")
                        .HasColumnType("timestamp with time zone")
                        .HasColumnName("updated");

                    b.HasKey("CorrelationId")
                        .HasName("pk_data_migration_state");

                    b.ToTable("data_migration_state", (string)null);
                });
#pragma warning restore 612, 618
        }
    }
}
