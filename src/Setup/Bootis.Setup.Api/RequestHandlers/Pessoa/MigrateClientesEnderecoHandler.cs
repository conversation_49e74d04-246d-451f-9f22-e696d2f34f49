namespace Bootis.Setup.Api.RequestHandlers.Pessoa;

public record ClienteEndereco(
    Guid Id,
    string ClienteIdentificacao,
    string PaisDescricao,
    string EstadoDescricao,
    string CidadeDescricao,
    string Bairro,
    string Cep,
    string Logradouro,
    string Numero,
    string Complemento,
    bool Principal,
    string Descricao);

public record MigrateClientesEndereco(
    Guid CorrelationId,
    string SourceConnectionString,
    Guid GroupTenantId,
    Guid TenantId,
    Guid CompanyId)
    : BaseRequest(CorrelationId, SourceConnectionString, GroupTenantId, TenantId, CompanyId);

public class MigrateClientesEnderecoHandler(IConfiguration configuration)
    : BaseRequestHandler<MigrateClientesEndereco>(configuration)
{
    public override async Task Handle(MigrateClientesEndereco request, CancellationToken cancellationToken)
    {
        var dataTable = await GetDataFromSourceDatabase(request, "SELECT * FROM clientes_endereco", cancellationToken);

        await InsertOrUpdateData(dataTable, "clientes_endereco", cancellationToken);
    }
}