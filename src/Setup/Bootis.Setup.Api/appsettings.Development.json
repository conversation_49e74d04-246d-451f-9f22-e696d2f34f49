{"App": {"Name": "Dev-SETUP"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information", "Bootis": "Debug"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}", "restrictedToMinimumLevel": "Debug"}}, {"Name": "File", "Args": {"path": "Logs/dev-log-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 3, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] ({ApplicationName}/{SourceContext}) User:{UserId} Tenant:{TenantId} Trace:{TraceId} Span:{SpanId} {Message:lj}{NewLine}{Exception}", "restrictedToMinimumLevel": "Debug"}}]}}