using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Bootis.Shared.Common.Configuration;
using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Common.Security;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;

namespace Bootis.Shared.Application.Configuration;

public class JwtGenerator(IOptions<SecurityConfiguration> jwtConfig) : IJwtGenerator
{
    public const string UserIdClaim = "uid";
    public const string SessionIdClaim = "sid";
    public const string TenantIdClaim = "tid";
    public const string EmailClaim = "email";
    public const string NameClaim = "name";
    public const string GroupTenantIdClaim = "gtid";

    private readonly SecurityConfiguration _jwtConfig = jwtConfig.Value;

    public string Generate(UserIdentity userIdentity, Guid sessionId, int expirationMinutes)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(_jwtConfig.JwtKey);

        var claims = new List<Claim>
        {
            new(UserIdClaim, userIdentity.UserId.ToString()),
            new(NameClaim, userIdentity.Name),
            new(EmailClaim, userIdentity.Email),
            new(TenantIdClaim, userIdentity.TenantId.ToString()),
            new(GroupTenantIdClaim, userIdentity.GroupTenantId.ToString()),
            new(SessionIdClaim, sessionId.ToString()),
            new("iat", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64),
            new("exp_minutes", expirationMinutes.ToString())
        };

        claims.AddRange(userIdentity.CustomClaims.Select(additionalClaim =>
            new Claim(additionalClaim.Key, additionalClaim.Value)));

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddMinutes(expirationMinutes),
            NotBefore = DateTime.UtcNow,
            Issuer = _jwtConfig.Issuer,
            Audience = _jwtConfig.Audience,
            SigningCredentials = new SigningCredentials(
                new SymmetricSecurityKey(key),
                SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }
}