using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Shared.Common.UnidadeMedida.ConversaoUnidadeMedida.Specializations;

public class ConversaoUnidadeMedidaKGParaMCG : ConversaoUnidadeMedida
{
    public override UnidadeMedidaAbreviacao UnidadeMedidaOrigem => UnidadeMedidaAbreviacao.kg;
    public override UnidadeMedidaAbreviacao UnidadeMedidaConversao => UnidadeMedidaAbreviacao.mcg;
    public override decimal TaxaConversao => 1000000000;
    public override TipoCalculoDensidade TipoCalculoDensidade => TipoCalculoDensidade.Nenhum;
}