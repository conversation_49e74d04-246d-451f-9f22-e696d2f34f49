using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Shared.Common.UnidadeMedida.ConversaoUnidadeMedida.Specializations;

public class ConversaoUnidadeMedidaMILHAOParaMILHAO : ConversaoUnidadeMedida
{
    public override UnidadeMedidaAbreviacao UnidadeMedidaOrigem => UnidadeMedidaAbreviacao.MILHAO;
    public override UnidadeMedidaAbreviacao UnidadeMedidaConversao => UnidadeMedidaAbreviacao.MILHAO;
    public override decimal TaxaConversao => 1;
    public override TipoCalculoDensidade TipoCalculoDensidade => TipoCalculoDensidade.Nenhum;
}