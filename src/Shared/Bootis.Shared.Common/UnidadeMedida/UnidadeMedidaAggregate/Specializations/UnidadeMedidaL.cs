using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Shared.Common.UnidadeMedida.UnidadeMedidaAggregate.Specializations;

public class UnidadeMedidaL : UnidadeMedida
{
    public override UnidadeMedidaAbreviacao Abreviacao => UnidadeMedidaAbreviacao.L;
    public override string Descricao => "Litro";
    public override bool Ativo => true;
    public override bool UnidadeAlternativa => false;
    public override bool UnidadeEstoque => true;
    public override bool UnidadePrescricao => true;
    public override bool UnidadePosologia => true;
    public override TipoUnidade TipoUnidade => TipoUnidade.Volume;
}