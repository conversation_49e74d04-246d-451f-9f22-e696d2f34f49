using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Shared.Common.UnidadeMedida.UnidadeMedidaAggregate.Specializations;

public class UnidadeMedidaKG : UnidadeMedida
{
    public override UnidadeMedidaAbreviacao Abreviacao => UnidadeMedidaAbreviacao.kg;
    public override string Descricao => "Quilograma";
    public override bool Ativo => true;
    public override bool UnidadeAlternativa => false;
    public override bool UnidadeEstoque => true;
    public override bool UnidadePrescricao => true;
    public override bool UnidadePosologia => false;
    public override TipoUnidade TipoUnidade => TipoUnidade.Peso;
}