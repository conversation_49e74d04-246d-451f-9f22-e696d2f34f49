using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Shared.Common.UnidadeMedida.TipoClasseProdutoAggregate.Specializations;

public class TipoClasseProdutoAcabado : TipoClasseProduto
{
    public override UnidadeMedidaAbreviacao UnidadeCalculo => UnidadeMedidaAbreviacao.un;
    public override UnidadeMedidaAbreviacao UnidadePadraoVisualizacao => UnidadeMedidaAbreviacao.un;
    public override bool ControlaEstoque => true;

    public override List<UnidadeMedidaAbreviacao> UnidadesVisualizacao =>
        new() { UnidadeMedidaAbreviacao.un, UnidadeMedidaAbreviacao.Mil };

    public override TipoClasseProdutoAbreviacao Abreviacao => TipoClasseProdutoAbreviacao.ProdutoAcabado;
}