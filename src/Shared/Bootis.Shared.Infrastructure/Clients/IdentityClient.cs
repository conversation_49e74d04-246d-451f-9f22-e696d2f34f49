using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using System.Text.Json.Serialization;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.ValueObjects.Auth;
using Microsoft.Extensions.Logging;
using OneOf;

namespace Bootis.Shared.Infrastructure.Clients;

public class IdentityClient(HttpClient httpClient, ILogger<IdentityClient> logger) : IIdentityClient
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNameCaseInsensitive = true,
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        ReferenceHandler = ReferenceHandler.IgnoreCycles,
        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
    };

    public async Task<OneOf<AuthorizeSuccess, AuthorizeFailure>> AuthorizeAsync(Guid sessionId, Guid userId,
        List<int> requiredRoles, CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogDebug("Starting token authorization with roles: {Roles}", string.Join(", ", requiredRoles));

            var request = new
            {
                sessionId,
                userId,
                requiredRoles
            };

            var response =
                await httpClient.PostAsJsonAsync("v1/oauth/authorize", request, JsonOptions, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                logger.LogWarning("Authorize failed with status {Status}: {Reason}",
                    response.StatusCode, response.ReasonPhrase);

                return CreateAuthorizeFailure(response.StatusCode, requiredRoles);
            }

            var json = await response.Content.ReadAsStringAsync(cancellationToken);

            using var doc = JsonDocument.Parse(json);
            var root = doc.RootElement;

            var isActivePropertyName = nameof(AuthorizeSuccess.IsActive).ToCamelCase();
            var authorizedPropertyName = nameof(AuthorizeSuccess.Authorized).ToCamelCase();

            var active = root.TryGetProperty(isActivePropertyName, out var activeProp) && activeProp.GetBoolean();
            var authorized = root.TryGetProperty(authorizedPropertyName, out var authProp) && authProp.GetBoolean();

            if (authorized)
            {
                logger.LogDebug("Authorization successful");
                var success = JsonSerializer.Deserialize<AuthorizeSuccess>(json, JsonOptions)
                              ?? new AuthorizeSuccess
                              {
                                  IsActive = active,
                                  Authorized = true,
                                  GrantedRoles = requiredRoles
                              };
                return success;
            }

            var failure = JsonSerializer.Deserialize<AuthorizeFailure>(json, JsonOptions);
            if (failure == null)
            {
                var errorCode = AuthenticationError.InsufficientPermissions;

                if (root.TryGetProperty("errorCode", out var errorCodeProp) &&
                    errorCodeProp.ValueKind == JsonValueKind.Number)
                {
                    var errorValue = errorCodeProp.GetInt32();
                    if (Enum.IsDefined(typeof(AuthenticationError), errorValue))
                        errorCode = (AuthenticationError)errorValue;
                }

                failure = new AuthorizeFailure(errorCode)
                {
                    IsActive = active,
                    MissingRoles = requiredRoles
                };
            }

            logger.LogDebug("Authorization failed: {ErrorCode} - {ErrorMessage}", failure.ErrorCode,
                failure.ErrorMessage);
            return failure;
        }
        catch (HttpRequestException ex)
        {
            logger.LogError(ex, "Network error during authorization");
            return new AuthorizeFailure(AuthenticationError.NetworkError)
            {
                ErrorCode = AuthenticationError.NetworkError,
                IsActive = false,
                MissingRoles = requiredRoles
            };
        }
        catch (TaskCanceledException ex)
        {
            logger.LogError(ex, "Authorization request timed out");
            return new AuthorizeFailure(AuthenticationError.ServiceUnavailable)
            {
                IsActive = false,
                MissingRoles = requiredRoles
            };
        }
        catch (JsonException ex)
        {
            logger.LogError(ex, "Failed to parse authorization response");
            return new AuthorizeFailure(AuthenticationError.InternalError)
            {
                IsActive = false,
                MissingRoles = requiredRoles
            };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error during authorization");
            return new AuthorizeFailure(AuthenticationError.InternalError)
            {
                IsActive = false,
                MissingRoles = requiredRoles
            };
        }
    }

    public async Task<OneOf<TokenValidationSuccess, TokenValidationFailure>> IntrospectAsync(string token,
        IntrospectType introspectType, CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogDebug("Starting token introspection");

            var request = new
            {
                token,
                Type = introspectType
            };
            var response =
                await httpClient.PostAsJsonAsync("v1/token/introspect", request, JsonOptions, cancellationToken);

            if (!response.IsSuccessStatusCode)
            {
                logger.LogWarning("Introspect failed with status {Status}: {Reason}",
                    response.StatusCode, response.ReasonPhrase);

                return CreateIntrospectFailure(response.StatusCode);
            }

            var json = await response.Content.ReadAsStringAsync(cancellationToken);

            using var doc = JsonDocument.Parse(json);
            var root = doc.RootElement;

            var isActivePropertyName = nameof(TokenValidationSuccess.IsActive).ToCamelCase();
            if (!root.TryGetProperty(isActivePropertyName, out var activeProp) || !activeProp.GetBoolean())
            {
                logger.LogDebug("Token is not active");
                return JsonSerializer.Deserialize<TokenValidationFailure>(json, JsonOptions)
                       ?? new TokenValidationFailure(AuthenticationError.InvalidToken);
            }

            var success = JsonSerializer.Deserialize<TokenValidationSuccess>(json, JsonOptions);
            if (success == null)
            {
                logger.LogWarning("Failed to deserialize successful introspect response");
                return new TokenValidationFailure(AuthenticationError.InternalError);
            }

            logger.LogDebug("Token introspection successful for user {UserId}",
                success.UserSession.UserIdentity.UserId);
            return success;
        }
        catch (HttpRequestException ex)
        {
            logger.LogError(ex, "Network error during token introspection");
            return new TokenValidationFailure(AuthenticationError.NetworkError);
        }
        catch (TaskCanceledException ex)
        {
            logger.LogError(ex, "Token introspection timed out");
            return new TokenValidationFailure(AuthenticationError.ServiceUnavailable);
        }
        catch (JsonException ex)
        {
            logger.LogError(ex, "Failed to parse introspect response");
            return new TokenValidationFailure(AuthenticationError.InternalError);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error during token introspection");
            return new TokenValidationFailure(AuthenticationError.InternalError);
        }
    }

    private static TokenValidationFailure CreateIntrospectFailure(HttpStatusCode statusCode)
    {
        var errorCode = MapHttpStatusToAuthError(statusCode);

        return new TokenValidationFailure(errorCode);
    }

    private static AuthorizeFailure CreateAuthorizeFailure(HttpStatusCode statusCode, IEnumerable<int> missingRoles)
    {
        var errorCode = MapHttpStatusToAuthError(statusCode);

        return new AuthorizeFailure(errorCode)
        {
            IsActive = statusCode != HttpStatusCode.Unauthorized,
            MissingRoles = missingRoles
        };
    }

    private static AuthenticationError MapHttpStatusToAuthError(HttpStatusCode statusCode)
    {
        return statusCode switch
        {
            HttpStatusCode.Unauthorized => AuthenticationError.InvalidToken,
            HttpStatusCode.Forbidden => AuthenticationError.InsufficientPermissions,
            HttpStatusCode.NotFound => AuthenticationError.InvalidToken,
            HttpStatusCode.BadRequest => AuthenticationError.MalformedToken,
            HttpStatusCode.InternalServerError => AuthenticationError.InternalError,
            HttpStatusCode.ServiceUnavailable => AuthenticationError.ServiceUnavailable,
            HttpStatusCode.RequestTimeout => AuthenticationError.ServiceUnavailable,
            _ => AuthenticationError.Unknown
        };
    }
}