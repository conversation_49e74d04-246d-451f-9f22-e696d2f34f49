using Asp.Versioning.ApiExplorer;
using Bootis.Shared.Api.Middlewares;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Events;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Prometheus;

namespace Bootis.Shared.Api.Extensions;

public static class ApplicationBuilderExtensions
{
    public static void ConfigureDefaultSetup(this IApplicationBuilder app, IConfiguration configuration)
    {
        app.ConfigureBaseDefaultSetup(configuration, a => { a.UseMiddleware<DomainEventMiddleware>(); });
    }

    public static void ConfigureBaseDefaultSetup(this IApplicationBuilder app, IConfiguration configuration,
        Action<IApplicationBuilder> configureMiddleware = null, Action<IApplicationBuilder> configureStatic = null)
    {
        var pathBase = configuration["PATH_BASE"];
        if (!string.IsNullOrWhiteSpace(pathBase))
            app.UsePathBase(pathBase);

        var env = app.ApplicationServices.GetRequiredService<IWebHostEnvironment>();

        if (env.IsDevelopment())
            app.UseDeveloperExceptionPage();

        configureStatic?.Invoke(app);

        app.ConfigureSwagger(pathBase);
        app.ConfigureLocalization();
        app.UseRouting();
        app.UseHttpMetrics();

        app.ConfigureAuthentication(configureMiddleware);
        app.ConfigureCors();
        app.ConfigureEndpoints();
        app.ConfigureHealthCheck();

        app.UseLocalizer();

        DomainEvent.Configure(app.ApplicationServices);
    }

    private static void ConfigureLocalization(this IApplicationBuilder app)
    {
        var locOptions = app.ApplicationServices.GetRequiredService<IOptions<RequestLocalizationOptions>>();
        app.UseRequestLocalization(locOptions.Value);
    }

    private static void ConfigureEndpoints(this IApplicationBuilder app)
    {
        app.UseEndpoints(builder =>
        {
            builder.MapDefaultControllerRoute();
            builder.MapControllers();
            builder.MapMetrics();
        });
    }

    private static void ConfigureSwagger(this IApplicationBuilder app, string pathBase)
    {
        var apiVersionDescriptionProvider =
            app.ApplicationServices.GetRequiredService<IApiVersionDescriptionProvider>();

        app.UseSwagger(c =>
        {
            c.PreSerializeFilters.Add((swaggerDoc, _) =>
            {
                if (!string.IsNullOrEmpty(pathBase))
                    swaggerDoc.Servers = new List<OpenApiServer>
                    {
                        new() { Url = pathBase }
                    };
            });
        });
        app.UseSwaggerUI(swaggerUiOptions =>
        {
            foreach (var groupName in apiVersionDescriptionProvider
                         .ApiVersionDescriptions
                         .Select(apiVersionDescription => apiVersionDescription.GroupName))
            {
                var url = string.IsNullOrEmpty(pathBase)
                    ? $"/swagger/{groupName}/swagger.json"
                    : $"{pathBase}/swagger/{groupName}/swagger.json";
                swaggerUiOptions.SwaggerEndpoint(url, groupName.ToUpperInvariant());
            }
        });
    }

    private static void ConfigureCors(this IApplicationBuilder app)
    {
        app.UseCors();
    }

    private static void ConfigureHealthCheck(this IApplicationBuilder app)
    {
        app.UseHealthChecks("/health");
    }

    private static void ConfigureAuthentication(this IApplicationBuilder app,
        Action<IApplicationBuilder> configureMiddleware = null)
    {
        app.UseAuthentication();
        app.UseAuthorization();

        app.UseMiddleware<SuccessResponseMiddleware>();

        configureMiddleware?.Invoke(app);
    }

    private static void UseLocalizer(this IApplicationBuilder app)
    {
        var localizer = app.ApplicationServices.GetRequiredService<IStringLocalizer>();
        Localizer.Initialize(localizer);
    }
}