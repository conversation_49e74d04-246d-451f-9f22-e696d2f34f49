using System.Runtime.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Bootis.Shared.Api.Converter.NewtonSoft;

public abstract class AbstractConverter<TClass, TEnum>(string discriminatorField) : JsonConverter<TClass>
    where TClass : class
    where TEnum : struct, Enum
{
    public override TClass ReadJson(JsonReader reader, Type objectType, TClass existingValue, bool hasExistingValue,
        JsonSerializer serializer)
    {
        var jo = JObject.Load(reader);

        if (!jo.TryGetValue(discriminatorField, StringComparison.InvariantCultureIgnoreCase, out var discriminator))
            throw new SerializationException("Not Found Discriminator");

        var typeId = GetTypeId(discriminator);

        return (TClass)jo.ToObject(GetType(typeId));
    }

    private static TEnum GetTypeId(JToken discriminator)
    {
        return discriminator.Type switch
        {
            JTokenType.Integer => (TEnum)Enum.ToObject(typeof(TEnum), discriminator.Value<int>()),
            JTokenType.String => Enum.Parse<TEnum>(discriminator.Value<string>()),
            _ => throw new SerializationException("Invalid value Discriminator")
        };
    }


    public override void WriteJson(JsonWriter writer, TClass value, JsonSerializer serializer)
    {
        var t = JToken.FromObject(value);

        if (t.Type != JTokenType.Object)
        {
            t.WriteTo(writer);
        }
        else
        {
            var o = (JObject)t;

            o.WriteTo(writer);
        }
    }


    protected abstract Type GetType(TEnum typeId);
}