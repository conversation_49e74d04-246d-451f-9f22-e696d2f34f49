using System.Net;
using Bootis.Shared.Api.Extensions;
using Bootis.Shared.Common.Exceptions;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ValidationException = FluentValidation.ValidationException;

namespace Bootis.Shared.Api.Filters;

public class HttpGlobalExceptionFilter(IWebHostEnvironment env, ILogger<HttpGlobalExceptionFilter> logger)
    : IExceptionFilter
{
    public void OnException(ExceptionContext context)
    {
        logger.LogError(new EventId(context.Exception.HResult),
            context.Exception,
            context.Exception.Message);

        switch (context.Exception)
        {
            case DomainException domainExceptions:
                ConfigureDomainException(context, domainExceptions);
                break;
            case ValidationException validationException:
                ConfigureValidationException(context, validationException);
                break;
            default:
                ConfigureException(context);
                break;
        }

        context.ExceptionHandled = true;
    }

    private static void ConfigureValidationException(ExceptionContext context,
        ValidationException validationException)
    {
        var problemDetails = new ValidationProblemDetails
        {
            Instance = context.HttpContext.Request.Path,
            Status = StatusCodes.Status400BadRequest,
            Detail = validationException.Errors.Any()
                ? "Consulte a propriedade [errors] para obter detalhes adicionais."
                : validationException.Message
        };

        foreach (var failure in validationException.Errors.GroupBy(c => c.PropertyName))
            problemDetails.Errors.Add(failure.Key, failure.Select(c => c.ErrorMessage).ToArray());

        context.Result = new BadRequestObjectResult(problemDetails);
        context.HttpContext.Response.StatusCode = (int)HttpStatusCode.BadRequest;
    }

    private static void ConfigureDomainException(ExceptionContext context, DomainException domainException)
    {
        var problemDetails = new CustomValidationProblemDetails
        {
            Instance = context.HttpContext.Request.Path,
            Status = StatusCodes.Status400BadRequest
        };

        switch (domainException.Type)
        {
            case TypeError.Validation:
            {
                problemDetails.Detail = "Consulte a propriedade [errors] para obter detalhes adicionais.";
                foreach (var failure in domainException.GetError().GroupBy(c => c.PropertyName))
                    problemDetails.Errors.Add(failure.Key, failure.Select(c => c.ErrorMessage).ToArray());
            }
                break;
            case TypeError.Object:
                problemDetails.Detail = "Consulte o codigo de erro informado na propriedade [errorCode]";
                problemDetails.Content = domainException.ErrorObjects;
                problemDetails.ErrorCode = $"{domainException.Prefix}-{domainException.ErrorCode}";

                break;
            default:
                problemDetails.Detail = "Consulte a propriedade [errors] para obter detalhes adicionais.";
                problemDetails.Errors.Add("DomainValidations", new[] { domainException.Message });
                break;
        }

        context.Result = new BadRequestObjectResult(problemDetails);
        context.HttpContext.Response.StatusCode = (int)HttpStatusCode.BadRequest;
    }

    private void ConfigureException(ExceptionContext context)
    {
        var json = new JsonErrorResponse
        {
            Messages = new[] { "Ocorreu um erro. Tente novamente." }
        };

        if (env.IsDevelopment()) json.DeveloperMessage = context.Exception.GetAllMessages();

        context.Result = new InternalServerErrorObjectResult(json);
        context.HttpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
    }
}