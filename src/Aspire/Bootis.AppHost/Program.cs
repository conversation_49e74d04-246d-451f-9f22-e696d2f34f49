using AppHost.Extensions;
using Projects;
using UUIDNext;

var builder = DistributedApplication.CreateBuilder(args);

var project = (Environment.GetEnvironmentVariable("ASPIRE_PROJECT") ?? "ALL").ToUpper();
var apiKey = Uuid.NewSequential().ToString("N");

// Cache de projetos para referências cruzadas
var projects = new Dictionary<string, IResourceBuilder<ProjectResource>>();

// Configurar SETUP
if (project is "ALL" or "SETUP")
{
    var setupDb = builder.GetPostgres().GetDatabase("SetupDBConnection", "SetupDB");
    var destinationDb = builder.GetPostgres().GetDatabase("DestinationConnectionString", "PharmaDB");

    var setupDependencies = new ProjectDependencies
    {
        Database = setupDb,
        RequiresRabbitMq = true,
        EnvironmentVariables = new Dictionary<string, string>
        {
            ["AutoMigrateDatabase"] = "true"
        }
    };

    var setup = builder.GetProject<Bootis_Setup_Api>("setup")
        .WithStandardInfrastructure(builder, setupDependencies)
        .WithReference(destinationDb)
        .WaitFor(destinationDb);

    projects["setup"] = setup;
}

// Configurar IDENTITY
if (project is "ALL" or "IDENTITY")
{
    var identityDb = builder.GetPostgres().GetDatabase("IdentityDBConnection", "IdentityDB");

    var identityDependencies = new ProjectDependencies
    {
        Database = identityDb,
        RequiresRabbitMq = true,
        RequiresRedis = true,
        EnvironmentVariables = new Dictionary<string, string>
        {
            ["AutoMigrateDatabase"] = "true"
        }
    };

    var identity = builder.GetProject<Bootis_Identity_Api>("identity")
        .WithStandardInfrastructure(builder, identityDependencies);

    projects["identity"] = identity;
}

// Configurar EMAIL
if (project is "ALL" or "EMAIL")
{
    var emailDependencies = new ProjectDependencies
    {
        RequiresRabbitMq = true
    };

    var email = builder.GetProject<Bootis_Email_Api>("email")
        .WithStandardInfrastructure(builder, emailDependencies);

    projects["email"] = email;
}

// Configurar CEP
if (project is "ALL" or "CEP")
{
    var cepDb = builder.GetPostgres().GetDatabase("CepDBConnection", "CepDB");

    var cepDependencies = new ProjectDependencies
    {
        Database = cepDb,
        RequiresRabbitMq = true,
        EnvironmentVariables = new Dictionary<string, string>
        {
            ["AutoMigrateDatabase"] = "true"
        }
    };

    var cep = builder.GetProject<Bootis_CEP_Api>("cep")
        .WithStandardInfrastructure(builder, cepDependencies);

    projects["cep"] = cep;
}

// Configurar PHARMA
if (project is "ALL" or "PHARMA")
{
    var pharmaDb = builder.GetPostgres().GetDatabase("PharmaDBConnection", "PharmaDB");

    var pharmaDependencies = new ProjectDependencies
    {
        Database = pharmaDb,
        RequiresRabbitMq = true,
        EnvironmentVariables = new Dictionary<string, string>
        {
            ["AutoMigrateDatabase"] = "true",
            ["Clients__Cep__BaseAddress"] = "http://cep/"
        }
    };

    var pharma = builder.GetProject<Bootis_Pharma_Api>("pharma")
        .WithStandardInfrastructure(builder, pharmaDependencies);

    projects["pharma"] = pharma;
}

// Configurar REPORT
if (project is "ALL" or "REPORT")
{
    var pharmaDb = builder.GetPostgres().GetDatabase("PharmaDBConnection", "PharmaDB");

    var reportDependencies = new ProjectDependencies
    {
        Database = pharmaDb,
        RequiresBlobStorage = true
    };

    var report = builder.GetProject<Bootis_Report_Web>("report")
        .WithExternalHttpEndpoints()
        .WithStandardInfrastructure(builder, reportDependencies);

    projects["report"] = report;
}

// Configurar NOTIFICATION
if (project is "ALL" or "NOTIFICATION")
{
    var notificationDb = builder.GetPostgres().GetDatabase("NotificationDBConnection", "NotificationDB");

    var notificationDependencies = new ProjectDependencies
    {
        Database = notificationDb,
        RequiresRabbitMq = true,
        EnvironmentVariables = new Dictionary<string, string>
        {
            ["AutoMigrateDatabase"] = "true"
        }
    };

    var notification = builder.GetProject<Bootis_Notification_Api>("notification")
        .WithStandardInfrastructure(builder, notificationDependencies);

    projects["notification"] = notification;
}

// Configurar referências cruzadas e segurança apenas para ALL
if (project == "ALL") ConfigureCrossReferences(projects, apiKey);

builder.Build().Run();

static void ConfigureCrossReferences(Dictionary<string, IResourceBuilder<ProjectResource>> projects, string apiKey)
{
    var identityUrl = "http://identity/";

    // Configurar referências do Identity como dependência
    if (projects.TryGetValue("identity", out var identity))
        identity
            .WithEnvironment("Security__IdentityServer", identityUrl)
            .WithEnvironment("Security__ApiKey", apiKey);

    // Setup precisa do Identity
    if (projects.TryGetValue("setup", out var setup) && projects.TryGetValue("identity", out var identityRef))
        setup.WithEnvironment("Security__IdentityServer", identityUrl)
            .WithEnvironment("Security__ApiKey", apiKey)
            .WithReference(identityRef);

    // Email precisa do Identity
    if (projects.TryGetValue("email", out var email) && projects.TryGetValue("identity", out identityRef))
        email.WithEnvironment("Security__IdentityServer", identityUrl)
            .WithEnvironment("Security__ApiKey", apiKey)
            .WithReference(identityRef);

    // CEP precisa do Identity
    if (projects.TryGetValue("cep", out var cep) && projects.TryGetValue("identity", out identityRef))
        cep.WithEnvironment("Security__IdentityServer", identityUrl)
            .WithEnvironment("Security__ApiKey", apiKey)
            .WithReference(identityRef);

    // Pharma precisa do Identity e CEP
    if (projects.TryGetValue("pharma", out var pharma))
    {
        if (projects.TryGetValue("identity", out identityRef))
            pharma.WithEnvironment("Security__IdentityServer", identityUrl)
                .WithEnvironment("Security__ApiKey", apiKey)
                .WithReference(identityRef);

        if (projects.TryGetValue("cep", out var cepRef)) pharma.WithReference(cepRef);
    }

    // Catalogo precisa do Identity
    if (projects.TryGetValue("catalogo", out var catalogo) && projects.TryGetValue("identity", out identityRef))
        catalogo.WithEnvironment("Security__IdentityServer", identityUrl)
            .WithEnvironment("Security__ApiKey", apiKey)
            .WithReference(identityRef);

    // Report precisa do Identity
    if (projects.TryGetValue("report", out var report) && projects.TryGetValue("identity", out identityRef))
        report.WithEnvironment("Security__IdentityServer", identityUrl)
            .WithEnvironment("Security__ApiKey", apiKey)
            .WithReference(identityRef);

    // Notification precisa do Identity
    if (projects.TryGetValue("notification", out var notification) && projects.TryGetValue("identity", out identityRef))
        notification.WithEnvironment("Security__IdentityServer", identityUrl)
            .WithEnvironment("Security__ApiKey", apiKey)
            .WithReference(identityRef);
}