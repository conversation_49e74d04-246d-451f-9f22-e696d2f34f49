[{"outputFileName": "wwwroot/css/site.thirdparty.bundle.css", "inputFiles": ["node_modules/bootstrap/dist/css/bootstrap.min.css"], "minify": {"enabled": false, "adjustRelativePaths": false}}, {"outputFileName": "wwwroot/css/dx-reporting-skeleton-screen.css", "inputFiles": ["node_modules/devexpress-reporting/dist/css/dx-reporting-skeleton-screen.css"], "minify": {"enabled": false, "adjustRelativePaths": false}}, {"outputFileName": "wwwroot/css/viewer.part.light.bundle.css", "inputFiles": ["node_modules/devextreme-dist/css/dx.material.blue.light.css", "node_modules/@devexpress/analytics-core/dist/css/dx-analytics.common.css", "node_modules/@devexpress/analytics-core/dist/css/dx-analytics.material.blue.light.css", "node_modules/devexpress-reporting/dist/css/dx-webdocumentviewer.css"], "minify": {"enabled": false, "adjustRelativePaths": false}}, {"outputFileName": "wwwroot/css/viewer.part.dark.bundle.css", "inputFiles": ["node_modules/devextreme-dist/css/dx.material.blue.dark.css", "node_modules/@devexpress/analytics-core/dist/css/dx-analytics.common.css", "node_modules/@devexpress/analytics-core/dist/css/dx-analytics.material.blue.dark.css", "node_modules/devexpress-reporting/dist/css/dx-webdocumentviewer.css"], "minify": {"enabled": false, "adjustRelativePaths": false}}, {"outputFileName": "wwwroot/css/designer.part.bundle.css", "inputFiles": ["node_modules/devexpress-richedit/dist/dx.richedit.css", "node_modules/@devexpress/analytics-core/dist/css/dx-querybuilder.css", "node_modules/devexpress-reporting/dist/css/dx-reportdesigner.css"], "minify": {"enabled": false, "adjustRelativePaths": false}}, {"outputFileName": "wwwroot/css/ace/ace.bundle.css", "inputFiles": ["node_modules/ace-builds/css/ace.css", "node_modules/ace-builds/css/theme/dreamweaver.css", "node_modules/ace-builds/css/theme/ambiance.css"], "minify": {"enabled": false, "adjustRelativePaths": false}}, {"outputFileName": "wwwroot/js/site.thirdparty.bundle.js", "inputFiles": ["node_modules/jquery/dist/jquery.min.js", "node_modules/bootstrap/dist/js/bootstrap.min.js"], "minify": {"enabled": false}, "sourceMap": false}, {"outputFileName": "wwwroot/js/reporting.thirdparty.bundle.js", "inputFiles": ["node_modules/knockout/build/output/knockout-latest.js", "node_modules/ace-builds/src-min-noconflict/ace.js", "node_modules/ace-builds/src-min-noconflict/ext-language_tools.js", "node_modules/ace-builds/src-min-noconflict/snippets/text.js"], "minify": {"enabled": false}, "sourceMap": false}, {"outputFileName": "wwwroot/js/viewer.part.bundle.js", "inputFiles": ["node_modules/devextreme-dist/js/dx.all.js", "node_modules/@devexpress/analytics-core/dist/js/dx-analytics-core.min.js", "node_modules/devexpress-reporting/dist/js/dx-webdocumentviewer.min.js"], "minify": {"enabled": false}, "sourceMap": false}, {"outputFileName": "wwwroot/js/designer.part.bundle.js", "inputFiles": ["node_modules/jszip/dist/jszip.min.js", "node_modules/devexpress-richedit/dist/dx.richedit.min.js", "node_modules/@devexpress/analytics-core/dist/js/dx-querybuilder.min.js", "node_modules/devexpress-reporting/dist/js/dx-reportdesigner.min.js"], "minify": {"enabled": false}, "sourceMap": false}]