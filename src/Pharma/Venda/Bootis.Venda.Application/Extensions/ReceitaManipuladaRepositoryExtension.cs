using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Venda.Resources;

namespace Bootis.Venda.Application.Extensions;

public static class ReceitaManipuladaRepositoryExtension
{
    public static async Task<ReceitaManipulada> ObterReceitaManipuladaAsync(
        this IReceitaManipuladaRepository repository, Guid id)
    {
        var receitaManipulada = await repository.GetByIdAsync(id);
        // var receitaManipulada = await repository.ObterReceitaManipuladaPorIdAsync(id);

        if (receitaManipulada is null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_ReceitaManipulada_GuidNaoEncontrado(id));

        return receitaManipulada;
    }
}