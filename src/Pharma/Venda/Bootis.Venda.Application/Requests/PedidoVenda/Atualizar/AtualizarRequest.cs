using Bootis.Compra.Domain.Enumerations;
using MediatR;

namespace Bootis.Venda.Application.Requests.PedidoVenda.Atualizar;

public class AtualizarRequest : IRequest
{
    public Guid PedidoVendaId { get; set; }
    public TipoDesconto? TipoDesconto { get; set; }
    public decimal? Desconto { get; set; }
    public decimal Taxa { get; set; }
    public decimal? ValorTotal { get; set; }
    public string Observacao { get; set; }
}