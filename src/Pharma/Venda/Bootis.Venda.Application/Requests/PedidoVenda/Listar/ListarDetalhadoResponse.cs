namespace Bootis.Venda.Application.Requests.PedidoVenda.Listar;

public class ListarDetalhadoResponse
{
    public Guid Id { get; set; }
    public int CodigoPedidoVenda { get; set; }
    public string NomeCliente { get; set; }
    public string CanalIcon { get; set; }
    public string CodigoAtendimento { get; set; }
    public Guid AtendimentoId { get; set; }
    public Guid StatusAtendimentoId { get; set; }
    public string StatusAtendimento { get; set; }
    public string StatusAtendimentoCorFundo { get; set; }
    public string StatusAtendimentoCorFonte { get; set; }
    public DateTime DataLancamento { get; set; }
    public string Atendente { get; set; }
    public decimal? ValorPedido { get; set; }
    public int StatusPedidoVenda { get; set; }
}