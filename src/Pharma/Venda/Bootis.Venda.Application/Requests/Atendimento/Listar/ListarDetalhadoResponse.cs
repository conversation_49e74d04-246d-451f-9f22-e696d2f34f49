namespace Bootis.Venda.Application.Requests.Atendimento.Listar;

public class ListarDetalhadoResponse
{
    public Guid Id { get; set; }
    public Guid CanalAtendimentoId { get; set; }
    public string DescricaoCanalAtendimento { get; set; }
    public string IdentificadorCanal { get; set; }
    public string CanalIcon { get; set; }
    public int CodigoAtendimento { get; set; }
    public string NomeCliente { get; set; }
    public DateTime DataCriacao { get; set; }
    public DateTime? DataAtualizacao { get; set; }
    public string NomeAtendente { get; set; }
    public Guid StatusAtendimentoId { get; set; }
    public string StatusAtendimentoDescricao { get; set; }
    public string StatusAtendimentoCorFundo { get; set; }
    public string StatusAtendimentoCorFonte { get; set; }
}