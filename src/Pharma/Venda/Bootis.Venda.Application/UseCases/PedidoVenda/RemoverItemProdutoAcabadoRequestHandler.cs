using Bootis.Shared.Application.Interfaces;
using Bootis.Venda.Application.Requests.PedidoVenda.Remover;
using Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate;
using MediatR;

namespace Bootis.Venda.Application.UseCases.PedidoVenda;

public class RemoverItemRequestHandler(
    IUnitOfWork unitOfWork,
    IPedidoVendaRepository pedidoVendaRepository)
    : IRequestHandler<RemoverItemRequest>
{
    public async Task Handle(RemoverItemRequest request, CancellationToken cancellationToken)
    {
        var pedidoVendaItem = await pedidoVendaRepository.ObterPedidoVendaItemPorIdAsync(request.PedidoVendaItemId);
        var pedidoVenda = await pedidoVendaRepository.ObterPedidoVendaPorIdAsync(pedidoVendaItem.PedidoVendaId);

        pedidoVenda.RemoverItem(pedidoVendaItem);

        await unitOfWork.SaveChangesAsync(cancellationToken);

        pedidoVenda.CalcularTotalizador();

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}