using Bootis.Compra.Domain.Enumerations;
using Bootis.Pessoa.Domain.AggregatesModel.ClienteAggregate;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Domain.SeedWork;
using Bootis.Venda.Domain.AggregatesModel.AtendimentoAggregate;
using Bootis.Venda.Domain.Enumerations;
using Bootis.Venda.Resources;

namespace Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate;

public class PedidoVenda() : Entity, IAggregateRoot, ITenant
{
    private static readonly StatusVenda[] StatusFinais =
        [StatusVenda.Aprovado, StatusVenda.Entregue, StatusVenda.EntregueParcial];

    private static readonly StatusVendaItem[] StatusItemCancelados =
        [StatusVendaItem.Reprovado, StatusVendaItem.Cancelado];

    public PedidoVenda(Atendimento atendimento) : this()
    {
        ClienteId = atendimento.ClienteId;
        VendedorId = atendimento.AtendenteId;
        Status = StatusVenda.Pendente;
        DataLancamento = DateTime.UtcNow;
        Desconto = 0;
        TipoDesconto = TipoDesconto.DescontoMonetario;

        InitializePedidoVendaProperties(atendimento);
        AdicionarHistorico(StatusVenda.Pendente, VendedorId, false);
    }

    public Guid AtendimentoId { get; private set; }
    public Guid ClienteId { get; private set; }
    public Guid VendedorId { get; private set; }
    public int SequenciaGroupTenant { get; private set; }
    public string Observacao { get; private set; }
    public DateTime? DataLancamento { get; private set; }
    public decimal Desconto { get; private set; }
    public decimal PercentualDesconto { get; private set; }
    public TipoDesconto TipoDesconto { get; set; }
    public decimal ValorDescontoTotal { get; private set; }
    public decimal ValorDescontoRateio { get; private set; }
    public StatusVenda Status { get; private set; }


    private IEnumerable<PedidoVendaItem> ItensNaoCancelados
    {
        get
        {
            var ignorarItensCancelados = StatusFinais.Contains(Status);
            return Itens.Where(i =>
                !ignorarItensCancelados && !StatusItemCancelados.Contains(i.Status) && i.Selecionado);
        }
    }

    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    private void InitializePedidoVendaProperties(Atendimento atendimento)
    {
        Atendimento = atendimento;
        Cliente = atendimento.Cliente;
        Totalizador = new PedidoVendaTotalizador(this);
    }

    #region Regras de Negócio

    public void Aprovar(Guid vendedorId)
    {
        VerificarStatusOrcamento();
        AtualizarStatus(StatusVenda.Aprovado, vendedorId, false);
        AtualizarStatusItemAprovado(StatusVendaItem.Aprovado);
    }

    public void Reprovar(Guid vendedorId)
    {
        VerificarStatusOrcamento();
        AtualizarStatus(StatusVenda.Reprovado, vendedorId, false);
        AtualizarStatusTodosItens(StatusVendaItem.Reprovado);
        ZerarValoresPedido();
    }

    public void Cancelar(Guid vendedorId)
    {
        VerificarStatusOrcamento();
        AtualizarStatus(StatusVenda.Cancelado, vendedorId, false);
        AtualizarStatusTodosItens(StatusVendaItem.Cancelado);
        ZerarValoresPedido();
    }

    public void Estornar(Guid vendedorId)
    {
        if (!new[] { StatusVenda.Aprovado, StatusVenda.Reprovado, StatusVenda.Cancelado }.Contains(Status))
            throw new DomainException(
                Localizer.Instance.GetMessage_PedidoVenda_NaoPodeSerEstornado(Status.GetDescription()));

        AtualizarStatus(StatusVenda.Orcamento, vendedorId, true);
        AtualizarStatusTodosItens(StatusVendaItem.Orcado);
        CalcularTotalizador();
    }

    #endregion

    #region Operações CRUD

    public void AdicionarItemProdutoAcabado(string descricao, Guid produtoId,
        Guid? prescritorId, DateTime? dataPrescicao, decimal quantidade,
        decimal valorUnitario, decimal valorDesconto, TipoDesconto tipoDesconto,
        StatusVendaItem status, bool selecionado, Guid atendenteId)
    {
        if (Status == StatusVenda.Pendente)
            AtualizarStatus(StatusVenda.Orcamento, atendenteId, false);
        else
            VerificarStatusOrcamento();

        PedidoVendaItemProdutoAcabado item = new(descricao, produtoId,
            prescritorId, dataPrescicao, quantidade, valorUnitario,
            valorDesconto, tipoDesconto, status, selecionado);

        AdicionarItem(item);
    }

    public void AdicionarItemReceitaManipulada(ReceitaManipulada receitaManipulada, decimal quantidade,
        string descricao,
        decimal valorUnitario, decimal valorDesconto, decimal valorTotalItem, TipoDesconto tipoDesconto,
        StatusVendaItem status,
        bool selecionado, Guid atendenteId)
    {
        if (Status == StatusVenda.Pendente)
            AtualizarStatus(StatusVenda.Orcamento, atendenteId, false);
        else
            VerificarStatusOrcamento();

        var pedidoVendaItemReceitaManipulada =
            new PedidoVendaItemReceitaManipulada(receitaManipulada, quantidade, descricao, valorUnitario, valorDesconto,
                valorTotalItem, tipoDesconto, status, selecionado);

        AdicionarItem(pedidoVendaItemReceitaManipulada);
    }

    private void AdicionarItem(PedidoVendaItem item)
    {
        item.Ordem = Itens.Count + 1;

        Itens.Add(item);

        CalcularTotalizador();
    }

    public void AtualizarItemProdutoAcabado(decimal quantidade, decimal valorUnitario, TipoDesconto tipoDesconto,
        decimal valorDesconto, Guid produtoId, Guid? prescritorId, DateTime? dataPrescricao,
        PedidoVendaItemProdutoAcabado item)
    {
        VerificarStatusOrcamento();

        item.AtualizarQuantidade(quantidade);
        item.AtualizarValorUnitario(valorUnitario);
        item.AlterarDesconto(tipoDesconto, valorDesconto);
        item.Atualizar(produtoId, prescritorId, dataPrescricao);

        CalcularTotalizador();
    }

    private void AtualizarStatusItemAprovado(StatusVendaItem status)
    {
        foreach (var item in Itens)
        {
            if (!item.Selecionado) item.Status = StatusVendaItem.Reprovado;

            item.Status = item.Status == StatusVendaItem.Orcado ? status : StatusVendaItem.Reprovado;
        }
    }

    private void AtualizarStatusTodosItens(StatusVendaItem status)
    {
        foreach (var item in Itens) item.Status = status;
    }

    public void AtualizarQuantidadeItem(PedidoVendaItem item, decimal quantidade)
    {
        VerificarStatusOrcamento();

        item.AtualizarQuantidade(quantidade);
    }

    public void AtualizarItemSelecionado(PedidoVendaItem item, bool selecionado)
    {
        VerificarStatusOrcamento();

        item.SelecionarItem(selecionado);

        if (!Itens.Any(i => i.Selecionado) && Itens.Contains(item))
            ZerarValoresPedido();
        else if (!selecionado) RemoverDescontoItensCancelados(item);
    }

    public void RemoverItem(PedidoVendaItem pedidoVendaItem)
    {
        VerificarStatusOrcamento();

        Itens.Remove(pedidoVendaItem);

        if (Itens.Count == 0)
        {
            ZerarValoresPedido();
            AtualizarStatus(StatusVenda.Pendente, VendedorId, false);
        }

        RemoverDescontoItensCancelados(pedidoVendaItem);
        AtualizarOrdemItens();
    }

    private void AtualizarOrdemItens()
    {
        var ordem = 1;
        foreach (var item in Itens.OrderBy(i => i.Ordem)) item.Ordem = ordem++;
    }

    public void AtualizarStatus(StatusVenda statusVenda, Guid vendedorId, bool estorno)
    {
        if (Status != statusVenda)
        {
            Status = statusVenda;

            PedidoVendaHistorico historico = new(vendedorId, statusVenda, estorno);
            Historico.Add(historico);
        }
    }

    private void AdicionarHistorico(StatusVenda statusVenda, Guid vendedorId, bool estorno)
    {
        var historico = new PedidoVendaHistorico(vendedorId, statusVenda, estorno);
        Historico.Add(historico);
    }

    private void VerificarStatusOrcamento()
    {
        if (Status != StatusVenda.Orcamento)
            throw new DomainException(
                Localizer.Instance.GetMessage_PedidoVenda_StatusInvalido(Status.GetDescription()));
    }

    public void AtualizarObservacao(string observacao)
    {
        Observacao = observacao;
    }

    public void AtualizarEntrega(
        TipoEntrega tipo, decimal taxa,
        decimal troco, string observacao,
        Guid? enderecoId, string descricao,
        bool principal, string numero,
        string cep, string complemento,
        string bairro, string logradouro,
        Guid? cidadeId, string cidadeDescricao,
        Guid? estadoId, string estadoDescricao,
        Guid? paisId, string paisDescricao)
    {
        Entrega = tipo switch
        {
            TipoEntrega.RetiradaBalcao => new Entrega(observacao),
            TipoEntrega.EntregaDomicilio => new PedidoVendaEntrega(descricao, principal, observacao,
                enderecoId.GetValueOrDefault(), taxa,
                troco, cep, bairro, complemento, logradouro, numero, cidadeId, cidadeDescricao, estadoId,
                estadoDescricao, paisId, paisDescricao),
            _ => throw new ArgumentOutOfRangeException(nameof(tipo), tipo, null)
        };

        CalcularTotalizador();
    }

    public void AtualizarEntrega(
        TipoEntrega tipo, decimal taxa,
        decimal troco, string observacao,
        Guid? enderecoId)
    {
        Entrega = tipo switch
        {
            TipoEntrega.RetiradaBalcao => new Entrega(observacao),
            TipoEntrega.EntregaDomicilio => new PedidoVendaEntrega(observacao, enderecoId.GetValueOrDefault(), taxa,
                troco),
            _ => throw new ArgumentOutOfRangeException(nameof(tipo), tipo, null)
        };

        CalcularTotalizador();
    }

    #endregion

    #region Cálculos

    public void AtualizarValorTotal(decimal valorTotal)
    {
        CalcularTotaisSemRateio();

        if (valorTotal > Totalizador.TotalProdutosLiquidoSemRateio + Entrega.GetTaxa())
            throw new DomainException(Localizer.Instance.GetMessage_PedidoVenda_ValorTotalInvalido());

        if (Totalizador.TotalPedido == valorTotal) return;

        var descontoTotal = Totalizador.TotalPedido + ValorDescontoRateio +
            Totalizador.TotalProdutosDescontosUnitarios - valorTotal;

        AtualizarDesconto(TipoDesconto.DescontoMonetario, descontoTotal);
    }

    public void AtualizarDesconto(TipoDesconto tipoDesconto, decimal desconto)
    {
        TipoDesconto = tipoDesconto;
        Desconto = desconto;

        switch (TipoDesconto)
        {
            case TipoDesconto.DescontoMonetario:
                ValorDescontoTotal = Desconto;
                PercentualDesconto = Totalizador.TotalProdutosBruto != 0
                    ? Desconto / Totalizador.TotalProdutosBruto * 100
                    : 0;
                break;

            case TipoDesconto.DescontoPorcentagem:
                ValorDescontoTotal = Desconto * Totalizador.TotalProdutosBruto / 100;
                PercentualDesconto = Desconto;
                break;

            default:
                ValorDescontoTotal = 0;
                break;
        }

        if (ValorDescontoTotal > Totalizador.TotalProdutosBruto)
            throw new DomainException(Localizer.Instance.GetMessage_PedidoVenda_ValorDescontoInvalido());

        CalcularRateio();
    }

    public void CalcularTotalizador()
    {
        CalcularTotaisSemRateio();

        Desconto = Totalizador.TotalProdutosDescontosUnitarios + ValorDescontoRateio;

        AtualizarDesconto(TipoDesconto, Desconto);
    }

    public void CalcularRateio()
    {
        CalcularTotaisSemRateio();
        CalcularRateioItens();
        CalcularTotaisComRateio();
    }

    private void CalcularRateioItens()
    {
        ValorDescontoRateio = ValorDescontoTotal - Totalizador.TotalProdutosDescontosUnitarios;

        switch (ValorDescontoRateio)
        {
            case < 0:
                throw new DomainException(Localizer.Instance.GetMessage_PedidoVenda_SomaDescontosInvalida());
            case 0:
                ZerarRateioProdutos();
                return;
        }

        decimal descontoRateadoTotal = 0;

        foreach (var item in ItensNaoCancelados)
        {
            var peso = item.ValorTotalItem / Totalizador.TotalProdutosLiquidoSemRateio;
            var descontoRateado = Math.Truncate(100 * ValorDescontoRateio * peso) / 100;

            item.AtualizarDescontoRateio(descontoRateado);
            descontoRateadoTotal += descontoRateado;
        }

        if (descontoRateadoTotal != ValorDescontoRateio)
        {
            var itemDeMaiorValor =
                ItensNaoCancelados.OrderByDescending(item => item.ValorTotalItem).First();
            var descontoRateio =
                itemDeMaiorValor.ValorDescontoRateio + (ValorDescontoRateio - descontoRateadoTotal);

            itemDeMaiorValor.AtualizarDescontoRateio(descontoRateio);
        }
    }

    private void CalcularTotaisSemRateio()
    {
        decimal totalProdutosDescontosUnitarios;
        var totalProdutosBruto = totalProdutosDescontosUnitarios = 0;

        foreach (var item in ItensNaoCancelados)
        {
            totalProdutosBruto += item.ValorUnitario * item.Quantidade;
            totalProdutosDescontosUnitarios += item.ValorDescontoUnitario * item.Quantidade;
        }

        var totalProdutosLiquidoSemRateio = totalProdutosBruto - totalProdutosDescontosUnitarios;

        var totalPedido = totalProdutosLiquidoSemRateio - ValorDescontoRateio + Entrega.GetTaxa();

        Totalizador.AtualizarTotalProdutosBruto(totalProdutosBruto);
        Totalizador.AtualizarTotalProdutosLiquidoSemRateio(totalProdutosLiquidoSemRateio);
        Totalizador.AtualizarTotalProdutosDescontosUnitarios(totalProdutosDescontosUnitarios);
        Totalizador.AtualizarTotalPedido(totalPedido);
    }

    private void CalcularTotaisComRateio()
    {
        decimal totalProdutosDescontosRateio;

        var totalProdutosLiquidoComRateio = totalProdutosDescontosRateio = 0;

        foreach (var item in ItensNaoCancelados)
        {
            totalProdutosLiquidoComRateio += item.ValorTotalItem - item.ValorDescontoRateio;
            totalProdutosDescontosRateio += item.ValorDescontoRateio;
        }

        var totalPedido = totalProdutosLiquidoComRateio + Entrega.GetTaxa();

        Totalizador.AtualizarTotalProdutosLiquidoComRateio(totalProdutosLiquidoComRateio);
        Totalizador.AtualizarTotalProdutosDescontosRateio(totalProdutosDescontosRateio);
        Totalizador.AtualizarTotalPedido(totalPedido);
    }

    private void ZerarRateioProdutos()
    {
        foreach (var item in Itens) item.AtualizarDescontoRateio(0);
    }

    private void ZerarValoresPedido()
    {
        Desconto = ValorDescontoTotal = ValorDescontoRateio = PercentualDesconto = 0;
        Totalizador.ZerarTotalizadores();
    }

    private void RemoverDescontoItensCancelados(PedidoVendaItem pedidoVendaItem)
    {
        Desconto += pedidoVendaItem.ValorDescontoUnitario * pedidoVendaItem.Quantidade -
                    pedidoVendaItem.ValorDescontoRateio;
    }

    #endregion

    #region Navigation properties

    public virtual ICollection<PedidoVendaHistorico> Historico { get; set; } = new List<PedidoVendaHistorico>();
    public virtual ICollection<PedidoVendaItem> Itens { get; set; } = new List<PedidoVendaItem>();
    public virtual PedidoVendaTotalizador Totalizador { get; set; }
    public virtual Entrega Entrega { get; set; } = new();
    public virtual Atendimento Atendimento { get; set; }
    public virtual Cliente Cliente { get; set; }

    #endregion
}