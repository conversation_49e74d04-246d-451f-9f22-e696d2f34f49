using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Venda.Domain.AggregatesModel.AtendimentoAggregate;

public class CanalAtendimento : Entity, IAggregateRoot
{
    public CanalAtendimento()
    {
    }

    public CanalAtendimento(string descricao, string icon)
    {
        Descricao = descricao;
        Icon = icon;
    }

    public string Descricao { get; set; }
    public string Icon { get; set; }

    #region Navigation properties

    public virtual ICollection<Atendimento> Atendimentos { get; set; } = new List<Atendimento>();

    #endregion
}