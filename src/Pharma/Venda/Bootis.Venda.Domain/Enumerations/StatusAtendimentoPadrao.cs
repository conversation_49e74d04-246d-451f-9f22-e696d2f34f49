using System.ComponentModel;

namespace Bootis.Venda.Domain.Enumerations;

public enum StatusAtendimentoPadrao
{
    [Description("Aguardando Atendimento")]
    AguardandoAtendimento = 1,
    [Description("Em Atendimento")] EmAtendimento = 2,
    [Description("Orçamento Enviado")] OrcamentoEnviado = 3,
    [Description("Atendimento Concluído")] AtendimentoConcluido = 4,
    [Description("Aguardando Receita")] AguardandoReceita = 5,
    Finalizado = 6
}