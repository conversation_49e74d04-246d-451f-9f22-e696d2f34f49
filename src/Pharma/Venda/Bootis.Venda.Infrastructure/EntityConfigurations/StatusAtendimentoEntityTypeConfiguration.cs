using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Bootis.Venda.Domain.AggregatesModel.StatusAtendimentoAggregate;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Venda.Infrastructure.EntityConfigurations;

public class StatusAtendimentoEntityTypeConfiguration : BaseEntityTypeConfiguration<StatusAtendimento>
{
    public override void Configure(EntityTypeBuilder<StatusAtendimento> builder)
    {
        builder.ToTable("status_atendimento");

        builder
            .Property(c => c.Descricao)
            .NomeDescricao(TamanhoTexto.Cinquenta)
            .IsRequired();

        builder
            .Property(c => c.Ativo)
            .HasDefaultValue(true)
            .IsRequired();

        builder
            .Property(c => c.Ordem)
            .IsRequired();

        builder
            .Property(c => c.UsuarioId)
            .IsRequired();

        builder
            .Property(c => c.<PERSON>r<PERSON>)
            .HasColumnName("cor_fonte")
            .HasMaxLength(100);

        builder
            .Property(c => c.CorFundo)
            .HasColumnName("cor_fundo")
            .HasMaxLength(100);

        builder
            .HasOne(c => c.Usuario)
            .WithMany()
            .HasForeignKey(c => c.UsuarioId)
            .OnDelete(DeleteBehavior.Restrict);

        base.Configure(builder);
    }
}