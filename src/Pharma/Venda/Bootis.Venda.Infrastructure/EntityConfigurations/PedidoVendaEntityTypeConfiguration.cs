using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Bootis.Shared.Infrastructure.ValueGenerators;
using Bootis.Venda.Domain.AggregatesModel.PedidoVendaAggregate;
using Bootis.Venda.Domain.Enumerations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Venda.Infrastructure.EntityConfigurations;

public class PedidoVendaEntityTypeConfiguration : BaseEntityTypeConfiguration<PedidoVenda>
{
    public override void Configure(EntityTypeBuilder<PedidoVenda> builder)
    {
        builder.ToTable("pedidos_venda");

        builder
            .Property(c => c.SequenciaGroupTenant)
            .HasValueGenerator<GroupTenantSequenceValueGenerator>();

        builder
            .HasOne(c => c.Atendimento)
            .WithOne()
            .HasForeignKey<PedidoVenda>(c => c.AtendimentoId)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired();

        builder
            .HasMany(c => c.Itens)
            .WithOne()
            .HasForeignKey(x => x.PedidoVendaId)
            .IsRequired();

        builder
            .HasOne(a => a.Cliente)
            .WithMany()
            .HasForeignKey(a => a.ClienteId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne<Usuario>()
            .WithMany()
            .HasForeignKey(c => c.VendedorId)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired();

        builder
            .Property(c => c.Observacao)
            .Observacao(TamanhoTexto.CentoECinquenta)
            .IsRequired(false);

        builder
            .Property(c => c.Status)
            .HasDefaultValue(StatusVenda.Pendente)
            .IsRequired();

        builder
            .Property(c => c.DataLancamento)
            .DataHora()
            .IsRequired();

        builder.HasOne(c => c.Entrega)
            .WithOne()
            .HasForeignKey<Entrega>("Id")
            .OnDelete(DeleteBehavior.Cascade);

        base.Configure(builder);
    }
}