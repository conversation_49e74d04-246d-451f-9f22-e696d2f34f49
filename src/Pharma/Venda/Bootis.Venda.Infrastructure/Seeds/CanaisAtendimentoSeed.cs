using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace Bootis.Venda.Infrastructure.Seeds;

public class CanaisAtendimentoSeed : ISeed
{
    public int Order => 1;

    public void Seed(DbContext dbContext)
    {
        dbContext.Database.ExecuteSqlRaw($"""
                                          INSERT INTO canais_atendimento (id, descricao, icon) 
                                          VALUES
                                              ('{1.ToGuid()}', 'Balcão', 'counter'),
                                              ('{2.ToGuid()}', 'Telefone', 'phone'),
                                              ('{3.ToGuid()}', 'WhatsApp', 'whatsapp'),
                                              ('{4.ToGuid()}', 'Facebook Messenger', 'facebook'),
                                              ('{5.ToGuid()}', 'Instagram', 'instagram'),
                                              ('{6.ToGuid()}', 'Telegram', 'telegram'),
                                              ('{7.ToGuid()}', 'E-mail', 'email'),
                                              ('{8.ToGuid()}', 'E-Commerce', 'shopping-bag-ecommerce'),
                                              ('{9.ToGuid()}', 'Chat E-Commerce', 'shopping-bag-chat'),
                                              ('{10.ToGuid()}', 'Aplicativo', 'mobile'),
                                              ('{11.ToGuid()}', 'Chat Aplicativo', 'mobile-chat');
                                          """);
    }
}