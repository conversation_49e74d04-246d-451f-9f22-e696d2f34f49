<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
        <LangVersion>default</LangVersion>
        <ProjectGuid>{03f59a59-df89-4597-9704-aa6cd4191647}</ProjectGuid>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Organizacional\Bootis.Organizacional.Domain\Bootis.Organizacional.Domain.csproj"/>
        <ProjectReference Include="..\..\Pessoa\Bootis.Pessoa.Domain\Bootis.Pessoa.Domain.csproj"/>
        <ProjectReference Include="..\Bootis.Estoque.Common\Bootis.Estoque.Common.csproj"/>
        <ProjectReference Include="..\Bootis.Estoque.Resources\Bootis.Estoque.Resources.csproj"/>
        <ProjectReference Include="..\..\Catalogo\Bootis.Catalogo.Domain\Bootis.Catalogo.Domain.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

</Project>

