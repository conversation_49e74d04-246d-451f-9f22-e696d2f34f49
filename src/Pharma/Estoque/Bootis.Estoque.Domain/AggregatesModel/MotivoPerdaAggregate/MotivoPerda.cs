using Bootis.Estoque.Domain.AggregatesModel.PerdaAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Estoque.Domain.AggregatesModel.MotivoPerdaAggregate;

public class MotivoPerda : Entity, IAggregateRoot
{
    public MotivoPerda()
    {
    }

    public MotivoPerda(string descricao,
        int codigoSngpc)
    {
        Descricao = descricao;
        CodigoSngpc = codigoSngpc;
    }

    public string Descricao { get; private set; }
    public int CodigoSngpc { get; private set; }
    public virtual ICollection<Perda> LancamentosPerda { get; set; } = new List<Perda>();
}