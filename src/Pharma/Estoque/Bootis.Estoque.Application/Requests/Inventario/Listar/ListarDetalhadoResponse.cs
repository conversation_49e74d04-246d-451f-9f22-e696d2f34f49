using System.Runtime.Serialization;

namespace Bootis.Estoque.Application.Requests.Inventario.Listar;

public class ListarDetalhadoResponse
{
    public Guid Id { get; set; }
    public int Codigo { get; set; }
    public DateTime DataCriacao { get; set; }
    public Guid ResponsavelId { get; set; }
    public string ResponsavelNomeCompleto { get; set; }
    public int StatusInventario { get; set; }
    public IEnumerable<LocalEstoqueResponse> LocaisEstoque { get; set; }
}

public class LocalEstoqueResponse
{
    [IgnoreDataMember] public Guid InventarioId { get; set; }

    public Guid? LocalEstoqueId { get; set; }
    public string LocalEstoqueDescricao { get; set; }
}