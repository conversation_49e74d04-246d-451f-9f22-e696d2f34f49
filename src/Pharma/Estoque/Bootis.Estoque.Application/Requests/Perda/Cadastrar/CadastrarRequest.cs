using Bootis.Shared.Common.UnidadeMedida.Enums;
using MediatR;

namespace Bootis.Estoque.Application.Requests.Perda.Cadastrar;

public class CadastrarRequest : IRequest
{
    public DateOnly DataPerda { get; set; }
    public Guid LoteId { get; set; }
    public Guid ProdutoId { get; set; }
    public Guid LocalEstoqueId { get; set; }
    public decimal Quantidade { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
    public Guid MotivoPerdaId { get; set; }
    public string Observacao { get; set; }
}