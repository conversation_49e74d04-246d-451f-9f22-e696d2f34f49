using Bootis.Estoque.Application.Requests.Lote.Listar;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Estoque.Application.Requests.Rastreabilidade.Listar;

public class ListarSaldoLotePorProdutoResponse
{
    public decimal TotalSaldo { get; set; }
    public string UnidadeMedida { get; set; }
    public PaginatedResult<ListarSaldos> Saldos { get; set; }
}

public class ListarSaldos
{
    public Guid LoteId { get; set; }
    public string LoteNumero { get; set; }
    public DateTime DataEntrada { get; set; }
    public DateTime DataValidade { get; set; }
    public string FornecedorDescricao { get; set; }
    public decimal Saldo { get; set; }
    public Domain.Enumerations.SituacaoLote Situacao { get; set; }

    public virtual ListarSituacaoLoteResponse SituacaoLote =>
        new()
        {
            Id = Situacao.ToInt(),
            Descricao = Enum.GetName(Situacao)
        };

    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; set; }
    public string UnidadeMedida { get; set; }
}