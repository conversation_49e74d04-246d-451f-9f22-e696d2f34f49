using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Requests.Lote.Atualizar;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.Lote;

public class AtualizarStatusRequestHandler(
    IUnitOfWork unitOfWork,
    ILoteRepository loteRepository) : IRequestHandler<AtualizarStatusRequest>
{
    public async Task Handle(AtualizarStatusRequest request, CancellationToken cancellationToken)
    {
        foreach (var loteId in request.LotesId)
        {
            var lote = await loteRepository.ObterLoteAsync(loteId).ConfigureAwait(false);

            lote.AtualizarSituacao(request.Situacao);

            loteRepository.Update(lote);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}