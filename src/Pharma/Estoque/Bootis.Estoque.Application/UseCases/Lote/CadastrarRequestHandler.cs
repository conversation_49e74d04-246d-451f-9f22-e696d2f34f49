using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Mappers;
using Bootis.Estoque.Application.Requests.Lote.Cadastrar;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.Domain.Dtos.Lote;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Localidade.Application.Extensions;
using Bootis.Localidade.Domain.AggregatesModel.PaisAggregate;
using Bootis.Organizacional.Application.Extensions;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Pessoa.Application.Extensions;
using Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Common.UnidadeMedida.TipoClasseProdutoAggregate;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.Lote;

public class CadastrarRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    ILoteRepository loteRepository,
    IProdutoRepository produtoRepository,
    IUsuarioRepository usuarioRepository,
    IFornecedorRepository fornecedorRepository,
    IPaisRepository paisRepository,
    ILocalEstoqueRepository localEstoqueRepository,
    ISaldoEstoqueRepository saldoEstoqueRepository,
    IEmpresaRepository empresaRepository)
    : IRequestHandler<CadastrarRequest, CadastrarResponse>
{
    public async Task<CadastrarResponse> Handle(CadastrarRequest request, CancellationToken cancellationToken)
    {
        var produto = await produtoRepository.ObterProdutoAsync(request.ProdutoId);
        var fornecedor = await fornecedorRepository.ObterFornecedorAsync(request.FornecedorId);
        var usuario = await usuarioRepository.ObterUsuarioAsync(userContext.UserId);

        await loteRepository.ValidarLotePorNumeroAsync(request.Numero, produto.Id, request.NumeroNf, request.SerieNf);

        var situacao = produto.ControlaQualidade ? SituacaoLote.ControleQualidade : SituacaoLote.Liberado;

        var lote = new Domain.AggregatesModel.LoteAggregate.Lote(request.Numero,
            produto,
            fornecedor,
            request.DataFabricacao,
            request.DataValidade,
            request.NumeroNf,
            request.SerieNf,
            usuario.Id,
            situacao);

        lote.ValidarDataValidade();


        loteRepository.Add(lote);

        if (produto.ClasseProdutoId == TipoClasseProdutoAbreviacao.MateriaPrima &&
            request.LoteInformacaoTecnica != null) await ProcessarCadastroInformacoesTecnicasAsync(request, lote);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);

        if (request.SaldoInicial != null) await CriarSaldoEstoque(produto, lote, request.SaldoInicial);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);

        return new CadastrarResponse
        {
            Id = lote.Id
        };
    }

    private async Task<LoteInformacaoTecnica> ProcessarCadastroInformacoesTecnicasAsync(CadastrarRequest request,
        Domain.AggregatesModel.LoteAggregate.Lote lote)
    {
        var pais = await paisRepository.ObterPaisAsync(request.LoteInformacaoTecnica.PaisOrigemId);
        var loteOrigem = await loteRepository.ObterLoteOrigemAsync(request.LoteInformacaoTecnica.LoteOrigemId);

        var loteInformacaoTecnica =
            lote.AtualizarLoteInformacaoTecnica(pais?.Id, loteOrigem, request.LoteInformacaoTecnica);

        if (request.LoteInformacaoTecnica.LoteUnidadeAlternativa1 is { UnidadeAlternativaId: > 0 })
        {
            var unidadeAlternativa1 =
                LoteUnidadeAlternativaMapper.LoteUnidadeAlternativaFrom(request.LoteInformacaoTecnica
                    .LoteUnidadeAlternativa1);
            loteInformacaoTecnica.AdicionarUnidadeAlternativa(unidadeAlternativa1);
        }

        if (request.LoteInformacaoTecnica.LoteUnidadeAlternativa2 is { UnidadeAlternativaId: > 0 })
        {
            var unidadeAlternativa2 =
                LoteUnidadeAlternativaMapper.LoteUnidadeAlternativaFrom(request.LoteInformacaoTecnica
                    .LoteUnidadeAlternativa2);
            loteInformacaoTecnica.AdicionarUnidadeAlternativa(unidadeAlternativa2);
        }

        loteRepository.Add(loteInformacaoTecnica);

        return loteInformacaoTecnica;
    }

    private async Task CriarSaldoEstoque(Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto produto,
        Domain.AggregatesModel.LoteAggregate.Lote lote, LoteSaldoInicialDto loteSaldoEstoqueDto)
    {
        var localEstoque =
            await localEstoqueRepository.ObterLocalEstoqueAsync(loteSaldoEstoqueDto.LocalEstoqueId);
        var empresa = await empresaRepository.ObterEmpresaAsync(userContext.TenantId);

        TipoClasseProdutoValidation.ValidarUnidadeMedidaPorClasseProduto(produto.ClasseProdutoId,
            loteSaldoEstoqueDto.UnidadeId);

        var saldoEstoque = new SaldoEstoque(empresa,
            produto,
            lote.Id,
            loteSaldoEstoqueDto.Quantidade,
            loteSaldoEstoqueDto.UnidadeId,
            localEstoque.Id);

        saldoEstoqueRepository.Add(saldoEstoque);
    }
}