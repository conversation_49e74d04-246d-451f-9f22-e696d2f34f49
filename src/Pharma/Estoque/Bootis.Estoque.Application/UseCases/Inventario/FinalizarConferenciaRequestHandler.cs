using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Requests.Inventario.Atualizar;
using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.Inventario;

public class FinalizarConferenciaRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IInventarioRepository inventarioRepository,
    IUsuarioRepository usuarioRepository)
    : IRequestHandler<FinalizarConferenciaRequest>
{
    public async Task Handle(FinalizarConferenciaRequest request, CancellationToken cancellationToken)
    {
        var inventario = await inventarioRepository.ObterInventarioAsync(request.InventarioId);
        var inventarioLancamento =
            inventario.InventarioLancamento.SingleOrDefault(c =>
                c.CodigoSequencia == inventario.CodigoUltimoLancamento);

        inventario.FinalizarConferencia(inventarioLancamento, request.Conferencia, userContext.UserId);

        inventarioRepository.Update(inventario);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}