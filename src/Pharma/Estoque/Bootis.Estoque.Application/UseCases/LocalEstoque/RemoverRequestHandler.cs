using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Requests.LocalEstoque.Remover;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.LocalEstoque;

public class RemoverRequestHandler(
    IUnitOfWork unitOfWork,
    ILocalEstoqueRepository localEstoqueRepository) : IRequestHandler<RemoverRequest>
{
    public async Task Handle(RemoverRequest request, CancellationToken cancellationToken)
    {
        var locaisEstoque = await localEstoqueRepository.ObterLocaisEstoqueAsync(request.LocaisEstoqueId);

        await localEstoqueRepository.VerificarDependenciasAsync(locaisEstoque);

        foreach (var localEstoqueId in request.LocaisEstoqueId)
        {
            var localEstoque = locaisEstoque.Single(c => c.Id == localEstoqueId);

            localEstoque.Remove();
            localEstoqueRepository.Remove(localEstoque);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}