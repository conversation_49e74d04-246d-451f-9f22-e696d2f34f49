using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Estoque.Application.Extensions;
using Bootis.Estoque.Application.Requests.MovimentoEstoque.Cadastrar;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.MovimentoEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.OperacaoEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.Domain.Enumerations;
using Bootis.Estoque.Resources;
using Bootis.Organizacional.Application.Extensions;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.ConversaoUnidadeMedida;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Common.UnidadeMedida.TipoClasseProdutoAggregate;
using Bootis.Shared.Common.UnidadeMedida.UnidadeMedidaAggregate;
using MediatR;

namespace Bootis.Estoque.Application.UseCases.MovimentoEstoque;

public class CadastrarRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IMovimentoEstoqueRepository movimentoEstoqueRepository,
    ILoteRepository loteRepository,
    ILocalEstoqueRepository localEstoqueRepository,
    IOperacaoEstoqueRepository operacaoEstoqueRepository,
    ISaldoEstoqueRepository saldoEstoqueRepository,
    IProdutoRepository produtoRepository,
    IEmpresaRepository empresaRepository)
    : IRequestHandler<CadastrarRequest, Guid>
{
    public async Task<Guid> Handle(CadastrarRequest request, CancellationToken cancellationToken)
    {
        var saldoEstoque = await ObterSaldoEstoqueAsync(request);
        var quantidadeConvertida =
            ConversaoQuantidade(request.UnidadeMedidaId, saldoEstoque.UnidadeMedidaId, request.Quantidade, 1);

        var movimentoEstoque = new Domain.AggregatesModel.MovimentoEstoqueAggregate.MovimentoEstoque(
            request.DataLancamento,
            request.EmpresaId,
            request.LoteId,
            request.OperacaoEstoqueId,
            request.TipoOperacao,
            request.LocalEstoqueId,
            request.Quantidade,
            request.UnidadeMedidaId,
            userContext.UserId,
            saldoEstoque,
            quantidadeConvertida);

        movimentoEstoqueRepository.Add(movimentoEstoque);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);

        return movimentoEstoque.Id;
    }

    private async Task<SaldoEstoque> ObterSaldoEstoqueAsync(CadastrarRequest request)
    {
        var lote = await loteRepository.ObterLotePorIdAsync(request.LoteId);
        var localEstoque = await localEstoqueRepository.ObterLocalEstoquePorIdAsync(request.LocalEstoqueId);

        var produto = await ValidarProdutoEstoque(request, lote);

        var unidadeMedida = UnidadeMedidaCreator.Criar(request.UnidadeMedidaId);

        if (unidadeMedida == null)
            throw new ValidationException(nameof(request.UnidadeMedidaId),
                Localizer.Instance.GetMessage_UnidadeMedida_GuidNaoEncontrado(request.UnidadeMedidaId.ToInt()));

        var saldoEstoque =
            await saldoEstoqueRepository.ObterSaldoEstoquePorLoteLocalEstoqueAsync(lote.Id, localEstoque.Id);

        if (saldoEstoque == null && request.TipoOperacao == TipoOperacao.Entrada)
            saldoEstoque = await CriarSaldoEstoque(produto, lote, localEstoque, request);

        if (saldoEstoque == null && request.TipoOperacao != TipoOperacao.Entrada)
            throw new ValidationException(nameof(lote.Id),
                Localizer.Instance.GetMessage_SaldoEstoque_IdNaoEncontrado(lote.Id, localEstoque.Id));

        if (request.OperacaoEstoqueId == Guid.Empty) return saldoEstoque;

        await operacaoEstoqueRepository.ObterOperacaoEstoquePorIdAsync(request.OperacaoEstoqueId);

        return saldoEstoque;
    }

    private async Task<SaldoEstoque> CriarSaldoEstoque(Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto produto,
        Domain.AggregatesModel.LoteAggregate.Lote lote,
        Domain.AggregatesModel.LocalEstoqueAggregate.LocalEstoque localEstoque, CadastrarRequest request)
    {
        var empresa = await empresaRepository.ObterEmpresaAsync(userContext.GroupTenantId);

        TipoClasseProdutoValidation.ValidarUnidadeMedidaPorClasseProduto(produto.ClasseProdutoId,
            request.UnidadeMedidaId);

        var saldoEstoque = new SaldoEstoque(empresa, produto, lote.Id, request.Quantidade, request.UnidadeMedidaId,
            localEstoque.Id);

        saldoEstoqueRepository.Add(saldoEstoque);

        return saldoEstoque;
    }

    private static decimal ConversaoQuantidade(UnidadeMedidaAbreviacao unidadeMedidaOrigemId,
        UnidadeMedidaAbreviacao unidadeMedidaConversaoId, decimal quantidade, decimal densidade)
    {
        var conversao = ConversaoUnidadeMedidaCreator.Criar(unidadeMedidaOrigemId, unidadeMedidaConversaoId);

        if (conversao is null)
            throw new ValidationException(nameof(unidadeMedidaConversaoId),
                Localizer.Instance.GetMessage_ConversaoUnidadeMedida_IdNaoEncontrado((int)unidadeMedidaConversaoId,
                    (int)unidadeMedidaConversaoId));

        return conversao.CalcularConversao(quantidade, densidade == 0 ? null : densidade);
    }

    private async Task<Catalogo.Domain.AggregatesModel.ProdutoAggregate.Produto> ValidarProdutoEstoque(
        CadastrarRequest request,
        Domain.AggregatesModel.LoteAggregate.Lote lote)
    {
        if (request.TipoOperacao != TipoOperacao.Entrada) return null;

        var produto = await produtoRepository.ObterProdutoPorIdAsync(lote.ProdutoId);

        if (produto.UnidadeEstoqueId == request.UnidadeMedidaId) return produto;

        request.Quantidade =
            ConversaoQuantidade(request.UnidadeMedidaId, produto.UnidadeEstoqueId, request.Quantidade, 1);
        request.UnidadeMedidaId = produto.UnidadeEstoqueId;

        return produto;
    }
}