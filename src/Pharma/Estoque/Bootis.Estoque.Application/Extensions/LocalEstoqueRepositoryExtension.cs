using Bootis.Estoque.Application.Requests.LocalEstoque.Remover;
using Bootis.Estoque.Common;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Estoque.Application.Extensions;

public static class LocalEstoqueRepositoryExtension
{
    public static async Task<LocalEstoque> ObterLocalEstoqueAsync(this ILocalEstoqueRepository repository,
        Guid id)
    {
        var localEstoque = await repository.ObterPorIdAsync(id);

        if (localEstoque is null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_LocalEstoque_GuidNaoEncontrado(id));

        return localEstoque;
    }

    public static async Task<List<LocalEstoque>> ObterLocaisEstoqueAsync(this ILocalEstoqueRepository repository,
        IEnumerable<Guid> ids)
    {
        var locaisEstoque = await repository.ObterPorIdsAsync(ids);

        var idsInvalidos = ids
            .Distinct()
            .Where(id => !locaisEstoque.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_LocalEstoque_GuidNaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(nameof(idsInvalidos), idsInvalidos);

        return locaisEstoque;
    }

    public static async Task<LocalEstoque> ObterLocalEstoquePorIdAsync(this ILocalEstoqueRepository repository, Guid id)
    {
        var localEstoque = await repository.GetByIdAsync(id);

        if (localEstoque is null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_LocalEstoque_IdNaoEncontrado(id));

        return localEstoque;
    }

    public static async Task ValidarLocalEstoquePorDescricaoEIdAsync(this ILocalEstoqueRepository repository,
        Guid id, string descricao, Guid empresaId)
    {
        var localEstoque = await repository.ObterLocalEstoquePorDescricaoAsync(descricao, id, empresaId);

        if (localEstoque is not null)
            throw new ValidationException(nameof(descricao),
                Localizer.Instance.GetMessage_LocalEstoque_DescricaoExistente(descricao, empresaId));
    }

    public static async Task ValidarLocalEstoquePorDescricaoAsync(this ILocalEstoqueRepository repository,
        string descricao, Guid empresaId)
    {
        var localEstoque = await repository.ObterLocalEstoquePorDescricaoCadastroAsync(descricao, empresaId);

        if (localEstoque is not null)
            throw new ValidationException(nameof(descricao),
                Localizer.Instance.GetMessage_LocalEstoque_DescricaoExistente(descricao, empresaId));
    }

    public static async Task VerificarDependenciasAsync(this ILocalEstoqueRepository repository,
        List<LocalEstoque> locais)
    {
        var removeResponse = new List<RemoverResponse>();

        foreach (var local in locais)
            if (await repository.VerificarDependenciaLocalEstoqueAsync(local.Id))
                removeResponse.Add(new RemoverResponse
                {
                    LocalEstoqueId = local.Id,
                    LocalEstoqueDescricao = local.Descricao,
                    EmpresaDescricao = local.Empresa.NomeFantasia
                });

        if (removeResponse.Count > 0)
            throw new DomainException(nameof(EstoqueErrorCode), (int)EstoqueErrorCode.LocalEstoque_Remover,
                removeResponse.ToArray());
    }
}