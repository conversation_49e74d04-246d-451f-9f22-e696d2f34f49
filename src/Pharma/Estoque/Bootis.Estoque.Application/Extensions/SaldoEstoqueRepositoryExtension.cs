using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LoteAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Estoque.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Estoque.Application.Extensions;

public static class SaldoEstoqueRepositoryExtension
{
    public static async Task<SaldoEstoque> ObterSaldoEstoquePorLoteELocalEstoqueAsync(
        this ISaldoEstoqueRepository repository,
        Guid loteId, Guid localEstoqueId)
    {
        var saldoEstoque = await repository.ObterSaldoEstoquePorLoteLocalEstoqueAsync(loteId, localEstoqueId);

        if (saldoEstoque == null)
            throw new ValidationException(nameof(saldoEstoque),
                Localizer.Instance.GetMessage_SaldoEstoque_IdNaoEncontrado(loteId, localEstoqueId));

        return saldoEstoque;
    }

    public static async Task<SaldoEstoque> ObterSaldoEstoquePorLoteELocalEstoqueIdAsync(
        this ISaldoEstoqueRepository repository,
        Guid loteId, Guid localEstoqueId)
    {
        var saldoEstoque =
            await repository.ObterSaldoEstoquePorLoteLocalEstoqueIdAsync(loteId,
                localEstoqueId);

        if (saldoEstoque == null)
            throw new ValidationException(nameof(saldoEstoque),
                Localizer.Instance.GetMessage_SaldoEstoqueLocal_GuidNaoEncontrado(loteId,
                    localEstoqueId));

        return saldoEstoque;
    }

    public static async Task ValidarSaldoEstoqueAsync(this ISaldoEstoqueRepository repository, Lote lote,
        LocalEstoque localEstoque,
        Guid loteId, decimal quantidade)
    {
        var saldoEstoque = await repository.ObterSaldoEstoquePorLoteLocalEstoqueAsync(lote.Id, localEstoque.Id);

        if (saldoEstoque == null)
            throw new ValidationException(nameof(loteId),
                Localizer.Instance.GetMessage_SaldoEstoque_IdNaoEncontrado(lote.Id, localEstoque.Id));

        if (saldoEstoque.Saldo < quantidade)
            throw new ValidationException(nameof(quantidade),
                Localizer.Instance.GetMessage_LancamentoPerda_SaldoEstoqueInsuficiente(quantidade));
    }
}