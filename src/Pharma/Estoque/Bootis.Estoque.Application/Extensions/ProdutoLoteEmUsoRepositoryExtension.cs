using Bootis.Estoque.Domain.AggregatesModel.ProdutoLoteEmUsoAggregate;
using Bootis.Estoque.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Estoque.Application.Extensions;

public static class ProdutoLoteEmUsoRepositoryExtension
{
    public static async Task<ProdutoLoteEmUso> ValidarLoteEmUsoAsync(
        this IProdutoLoteEmUsoRepository repository, Guid produtoId,
        Guid loteId, Guid localEstoqueExtenalId)
    {
        var loteEmUso = await repository.ObterLoteEmUsoAsync(produtoId,
            loteId, localEstoqueExtenalId).ConfigureAwait(false);

        if (loteEmUso is not null)
            throw new ValidationException(nameof(loteId),
                Localizer.Instance.GetMessage_ProdutoLoteEmUso_JaAssociado());

        return null;
    }
}