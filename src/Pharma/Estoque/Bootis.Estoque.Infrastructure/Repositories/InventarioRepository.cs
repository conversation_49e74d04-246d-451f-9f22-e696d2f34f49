using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Estoque.Domain.AggregatesModel.SaldoEstoqueAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace Bootis.Estoque.Infrastructure.Repositories;

public class InventarioRepository(IUserContext userContext, IDbContext context)
    : Repository<Inventario>(context), IInventarioRepository
{
    public async Task<Inventario> ObterInventarioPorIdAsync(Guid id)
    {
        return await DbSet
            .Include(c => c.Responsavel)
            .Include(c => c.InventarioEspecificacao)
            .Include(c => c.InventarioLancamento)
            .ThenInclude(c => c.InventarioItem)
            .ThenInclude(c => c.SaldoEstoque)
            .FirstOrDefaultAsync(c =>
                c.Id == id && c.GroupTenantId == userContext.GroupTenantId);
    }

    public async Task<IEnumerable<SaldoEstoque>> ObterSaldosEstoqueVinculadosAsync(Guid inventarioId)
    {
        return await Context.Set<SaldoEstoque>()
            .FromSqlInterpolated($@"
                SELECT se.id,
                       se.produto_id,
                       se.lote_id,
                       se.empresa_id,
                       se.local_estoque_id,
                       se.unidade_medida_id,
                       se.saldo,
                       se.bloqueado,
                       se.tenant_id,
                       se.group_tenant_id,
                       se.is_removed
                  FROM saldos_estoque se
                       JOIN produtos prod ON prod.id = se.produto_id
                       JOIN lotes lot ON lot.id = se.lote_id
                       JOIN locais_estoque le ON le.id = se.local_estoque_id
                       LEFT JOIN inventario_especificacoes inve ON inve.local_estoque_id = le.id OR inve.local_estoque_id IS NULL
                       LEFT JOIN inventarios inv ON inv.id = inve.inventario_id
                       LEFT JOIN sub_grupos subg ON subg.id = prod.sub_grupo_id
                       LEFT JOIN produto_grupos grup ON grup.id = subg.grupo_id 
                 WHERE inv.id = {inventarioId}
                   AND inv.group_tenant_id = {userContext.GroupTenantId}
                   AND (
                       EXISTS (
                           SELECT 1
                           FROM inventario_especificacoes i
                           WHERE i.inventario_id = inv.id
                             AND i.local_estoque_id IS NULL
                       )
                       OR
                       (inve.local_estoque_id IS NOT NULL AND se.local_estoque_id = inve.local_estoque_id)
                   )
                   AND (
                       (inve.grupo_id IS NOT NULL AND grup.id = inve.grupo_id AND 
                        (inve.sub_grupo_id IS NULL OR prod.sub_grupo_id = inve.sub_grupo_id))
                       OR 
                       (inve.grupo_id IS NULL AND inve.sub_grupo_id IS NOT NULL AND prod.sub_grupo_id = inve.sub_grupo_id)
                       OR
                       (inve.grupo_id IS NULL AND inve.sub_grupo_id IS NULL)
                   )
            ")
            .ToListAsync();
    }
}