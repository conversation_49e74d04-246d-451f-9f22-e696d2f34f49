using Bootis.Estoque.Domain.AggregatesModel.InventarioAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Extensions;
using Bootis.Shared.Infrastructure.ValueGenerators;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Estoque.Infrastructure.EntityConfigurations;

public class InventarioEntityTypeConfiguration : BaseEntityTypeConfiguration<Inventario>
{
    public override void Configure(EntityTypeBuilder<Inventario> builder)
    {
        builder.ToTable("inventarios");

        builder
            .Property(c => c.SequenciaGroupTenant).HasValueGenerator<GroupTenantSequenceValueGenerator>();

        builder
            .HasOne(c => c.Responsavel)
            .WithMany()
            .HasForeignKey(c => c.ResponsavelId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(c => c.DataCriacao)
            .DataHora()
            .IsRequired();

        builder
            .Property(c => c.OcultarQuantidadeLancamento)
            .HasDefaultValue(false);

        builder
            .Property(c => c.OcultarQuantidadeImpressao)
            .HasDefaultValue(false);

        builder
            .Property(c => c.StatusInventario)
            .IsRequired();

        builder
            .Property(c => c.CodigoUltimoLancamento)
            .IsRequired();

        builder
            .Ignore(c => c.IsRemoved);

        base.Configure(builder);
    }
}