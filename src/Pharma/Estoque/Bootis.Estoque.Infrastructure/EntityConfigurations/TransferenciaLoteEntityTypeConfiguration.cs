using Bootis.Estoque.Domain.AggregatesModel.TransferenciaLoteAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Bootis.Shared.Infrastructure.ValueGenerators;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Estoque.Infrastructure.EntityConfigurations;

public class TransferenciaLoteCabecalhoEntityConfiguration : BaseEntityTypeConfiguration<TransferenciaLote>
{
    public override void Configure(EntityTypeBuilder<TransferenciaLote> builder)
    {
        builder.ToTable("transferencias_lote");

        builder.Property(c => c.SequenciaNumeroTransferencia).HasValueGenerator<GroupTenantSequenceValueGenerator>();

        builder
            .Property(c => c.DataTransferencia)
            .DataHora()
            .IsRequired();

        builder
            .HasOne(c => c.Usuario)
            .WithMany()
            .HasForeignKey(c => c.UsuarioId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(c => c.LocalEstoqueOrigem)
            .WithMany()
            .HasForeignKey(m => m.LocalDeEstoqueOrigemId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder.HasOne(c => c.LocalEstoqueDestino)
            .WithMany()
            .HasForeignKey(m => m.LocalDeEstoqueDestinoId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder
            .Property(c => c.Observacao)
            .NomeDescricao(TamanhoTexto.Mil);

        base.Configure(builder);
    }
}