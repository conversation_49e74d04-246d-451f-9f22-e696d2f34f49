using System.Data;
using Bootis.Estoque.Application.Requests.Inventario.Obter;
using Bootis.Estoque.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.Inventario.Obter;

public class ObterDetalhesRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ObterDetalhesRequest, ObterDetalhesResponse>
{
    public async Task<ObterDetalhesResponse> Handle(ObterDetalhesRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT inv.sequencia_group_tenant AS codigo,
                                  inv.status_inventario AS status_inventario_id
                             FROM inventarios inv
                            WHERE inv.id = @id
                              AND inv.group_tenant_id = @groupTenantId;

                           SELECT le.id AS local_estoque_id,
                                  le.descricao AS local_estoque_descricao,
                                  le.id AS local_estoque_id
                             FROM locais_estoque le
                            WHERE le.ativo = true
                              AND le.id IN ( SELECT inve.local_estoque_id
                                               FROM inventarios inv
                                                    JOIN inventario_especificacoes inve ON inve.inventario_id = inv.id
                                              WHERE inv.id = @id
                                                AND inv.group_tenant_id = @groupTenantId
                                                AND inve.local_estoque_id IS NOT NULL)
                              OR EXISTS ( SELECT 1
                                               FROM inventarios inv
                                                    JOIN inventario_especificacoes inve ON inve.inventario_id = inv.id
                                              WHERE inv.id = @id
                                                AND inv.group_tenant_id = @groupTenantId
                                                AND inve.local_estoque_id IS NULL);

                           SELECT prod.id AS produto_id,
                                  prod.sequencia_group_tenant AS produto_codigo,
                                  prod.descricao AS produto_descricao,
                                  prod.classe_produto_id,
                                  un.id AS unidade_medida_id,
                                  un.abreviacao AS unidade_medida_abreviacao,
                                  lot.id AS lote_id,
                                  lot.numero AS lote_numero,
                                  CONCAT(lot.numero_nf, '/', lot.serie_nf) AS nota_fiscal_numero,
                                  CASE
                                      WHEN (inv.status_inventario = 1 OR inv.status_inventario = 2) AND inv.ocultar_quantidade_lancamento = true 
                                          THEN NULL
                                      ELSE se.saldo
                                  END AS quantidade,
                                  le.id AS local_estoque_id
                             FROM saldos_estoque se
                                  JOIN produtos prod ON prod.id = se.produto_id
                                  JOIN unidades_medida un ON un.id = prod.unidade_estoque_id
                                  JOIN lotes lot ON lot.id = se.lote_id
                                  JOIN locais_estoque le ON le.id = se.local_estoque_id
                                  LEFT JOIN inventario_especificacoes inve ON inve.local_estoque_id = le.id OR inve.local_estoque_id IS NULL
                                  LEFT JOIN inventarios inv ON inv.id = inve.inventario_id
                                  LEFT JOIN sub_grupos subg ON subg.id = prod.sub_grupo_id
                                  LEFT JOIN produto_grupos grup ON grup.id = subg.grupo_id 
                            WHERE inv.id = @id
                              AND inv.group_tenant_id = @groupTenantId
                              AND (
                                  EXISTS (
                                      SELECT 1
                                        FROM inventario_especificacoes i
                                       WHERE i.inventario_id = inv.id
                                         AND i.local_estoque_id IS NULL
                                   )
                                   OR
                                   (inve.local_estoque_id IS NOT NULL AND se.local_estoque_id = inve.local_estoque_id)
                                  )
                              AND (
                                    (inve.grupo_id IS NOT NULL AND grup.id = inve.grupo_id AND 
                                    (inve.sub_grupo_id IS NULL OR prod.sub_grupo_id = inve.sub_grupo_id))
                                   OR 
                                    (inve.grupo_id IS NULL AND inve.sub_grupo_id IS NOT NULL AND prod.sub_grupo_id = inve.sub_grupo_id)
                                   OR
                                    (inve.grupo_id IS NULL AND inve.sub_grupo_id IS NULL));
                           """;

        await using var query = await connection
            .QueryMultipleAsync(sql,
                new { request.Id, groupTenantId = userContext.GroupTenantId })
            .ConfigureAwait(false);

        var response = await query.ReadSingleOrDefaultAsync<ObterDetalhesResponse>();

        if (response is null)
        {
            var message = Localizer.Instance.GetMessage_Inventario_GuidNaoEncontrado(request.Id);

            throw new DomainException(message);
        }

        response.Especificacao = await query.ReadAsync<DetalhesEspecificacaoResponse>();
        var itens = await query.ReadAsync<DetalhesItensResponse>();

        foreach (var especificacao in response.Especificacao)
            especificacao.Itens = itens.Where(c => c.LocalEstoqueId == especificacao.LocalEstoqueId);

        return response;
    }
}