using System.Data;
using Bootis.Estoque.Application.Requests.Inventario.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.FilterFields;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.Inventario.Listar;

public class
    ListarSubGrupoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarSubGrupoRequest, PaginatedResult<ListarSubGrupoResponse>>
{
    public Task<PaginatedResult<ListarSubGrupoResponse>> Handle(ListarSubGrupoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT DISTINCT 
                               sub.descricao AS sub_grupo_descricao,
                               sub.id AS sub_grupo_id
                             FROM sub_grupos sub
                                  LEFT JOIN produto_grupos gru ON
                                        gru.id = sub.grupo_id
                                  LEFT JOIN produtos prod ON
                                        prod.sub_grupo_id = sub.id
                                  LEFT JOIN saldos_estoque se ON
                                        se.produto_id = prod.id
                                  LEFT JOIN locais_estoque lc ON
                                        lc.id = se.local_estoque_id
                            WHERE gru.group_tenant_id = @GroupTenantId
                                    !@SEARCH_CONDITION@!
                           """;

        var conditionLocalEstoqueId = new ConditionalFilterField<ListarSubGrupoRequest>
        {
            Filter = "LC.id = @LocalEstoqueId",
            Predicate = filter => filter.LocalEstoqueId is not null
        };

        var conditionGrupoId = new ConditionalFilterField<ListarSubGrupoRequest>
        {
            Filter = "GRU.id = @GrupoId",
            Predicate = filter => filter.GrupoId is not null
        };

        var searchDescricao = new StringSearchField
        {
            Field = "SUB.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarSubGrupoRequest, ListarSubGrupoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchDescricao)
            .AddFilter(conditionLocalEstoqueId)
            .AddFilter(conditionGrupoId)
            .ExecuteAsync();
    }
}