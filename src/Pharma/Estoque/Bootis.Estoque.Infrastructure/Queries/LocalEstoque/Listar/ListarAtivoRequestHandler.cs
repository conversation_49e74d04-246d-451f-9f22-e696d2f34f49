using System.Data;
using Bootis.Estoque.Application.Requests.LocalEstoque.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.LocalEstoque.Listar;

public class ListarAtivoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarAtivoRequest, PaginatedResult<ListarAtivoResponse>>
{
    public Task<PaginatedResult<ListarAtivoResponse>> Handle(ListarAtivoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT l.descricao,
                                  l.id,
                                  emp.nome_fantasia AS nome_empresa
                           FROM locais_estoque l
                           LEFT JOIN empresas emp ON emp.id = l.empresa_id
                           WHERE l.ativo = true 
                             AND l.group_tenant_id = @GroupTenantId 
                             !@SEARCH_CONDITION@!
                           """;

        var searchLocal = new StringSearchField
        {
            Field = "L.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarAtivoRequest, ListarAtivoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchLocal)
            .ExecuteAsync();
    }
}