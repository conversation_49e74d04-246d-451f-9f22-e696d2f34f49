using System.Data;
using Bootis.Estoque.Application.Requests.MovimentoEstoque.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.FilterFields;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Estoque.Infrastructure.Queries.MovimentoEstoque.Listar;

public class ListarDetalhadoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarDetalhadoRequest, PaginatedResult<ListarDetalhadoResponse>>
{
    public Task<PaginatedResult<ListarDetalhadoResponse>> Handle(ListarDetalhadoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                            WITH MOVIMENTO_ESTOQUE AS (
                           SELECT tl.id,
                                  tl.data_transferencia AS data_movimentacao,
                                  p.id AS produto_id,
                                  p.descricao AS produto_descricao,
                                  g.id AS grupo_id,
                                  s.id AS subgrupo_id,
                                  le.id AS local_estoque_id,
                                  le.descricao AS local_estoque_descricao,
                                  lot.id AS lote_id,
                                  lot.numero AS lote_numero,
                                  f.id AS fornecedor_id,
                                  f.nome AS nome_fornecedor,
                                  CONCAT(lot.numero_nf, '-', lot.serie_nf) AS nota_fiscal_numero,
                                  CAST('Transferência' AS TEXT) AS operacao,
                                  CAST(1 AS NUMERIC) AS tipo_movimento_id,
                                  tli.quantidade_transferida AS quantidade_movimento,
                                  tli.unidade_medida_id,
                                  un.abreviacao AS unidade_medida_abreviacao,
                                  u.id AS operador_id,
                                  CONCAT(u.nome, ' ', u.sobrenome) AS operador_nome,
                                  (tli.quantidade_transferida * p.valor_custo) AS custo_movimentacao
                             FROM transferencias_lote tl
                                  JOIN usuarios u on u.id = tl.usuario_id
                                  JOIN transferencias_lote_itens tli ON tli.transferencia_lote_id = tl.id
                                  JOIN lotes lot ON lot.id = tli.lote_id
                                  JOIN fornecedores f on f.id = lot.fornecedor_id
                                  JOIN produtos p on p.id = lot.produto_id
                                  JOIN sub_grupos s on s.id = p.sub_grupo_id
                                  JOIN produto_grupos g on g.id = s.grupo_id
                                  JOIN movimentos_estoque meo ON meo.id = tli.movimento_estoque_origem_id
                                  JOIN movimentos_estoque med ON med.id = tli.movimento_estoque_destino_id
                                  JOIN unidades_medida un ON un.id = tli.unidade_medida_id
                                  JOIN locais_estoque le ON le.id = meo.local_estoque_id
                            WHERE lot.group_tenant_id = @GroupTenantId 

                           UNION ALL

                           SELECT tl.id,
                                  tl.data_transferencia AS data_movimentacao,
                                  p.id AS produto_id,
                                  p.descricao AS produto_descricao,
                                  g.id AS grupo_id,
                                  s.id AS subgrupo_id,
                                  led.id AS local_estoque_id,
                                  led.descricao AS local_estoque_descricao,
                                  lot.id AS lote_id,
                                  lot.numero AS lote_numero,
                                  f.id AS fornecedor_id,
                                  f.nome AS nome_fornecedor,
                                  CONCAT(lot.numero_nf, '-', lot.serie_nf) AS nota_fiscal_numero,
                                  CAST('Transferência' AS TEXT) AS operacao,
                                  CAST(0 AS NUMERIC) AS tipo_movimento_id,
                                  tli.quantidade_transferida AS quantidade_movimento,
                                  tli.unidade_medida_id,
                                  un.abreviacao AS unidade_medida_abreviacao,
                                  u.id AS operador_id,
                                  CONCAT(u.nome, ' ', u.sobrenome) AS operador_nome,
                                  (tli.quantidade_transferida * p.valor_custo) AS custo_movimentacao
                             FROM transferencias_lote tl
                                  JOIN usuarios u on u.id = tl.usuario_id
                                  JOIN transferencias_lote_itens tli ON tli.transferencia_lote_id = tl.id
                                  JOIN lotes lot ON lot.id = tli.lote_id
                                  JOIN fornecedores f on f.id = lot.fornecedor_id
                                  JOIN produtos p on p.id = lot.produto_id
                                  JOIN sub_grupos s on s.id = p.sub_grupo_id
                                  JOIN produto_grupos g on g.id = s.grupo_id
                                  JOIN movimentos_estoque meo ON meo.id = tli.movimento_estoque_origem_id
                                  JOIN movimentos_estoque med ON med.id = tli.movimento_estoque_destino_id
                                  JOIN unidades_medida un ON un.id = tli.unidade_medida_id
                                  JOIN locais_estoque led ON led.id = med.local_estoque_id
                            WHERE lot.group_tenant_id = @GroupTenantId 

                           UNION ALL

                           SELECT per.id,
                                  per.data_perda AS data_movimentacao,
                                  p.id AS produto_id,
                                  p.descricao AS produto_descricao,
                                  g.id AS grupo_id,
                                  s.id AS subgrupo_id,
                                  le.id AS local_estoque_id,
                                  le.descricao AS local_estoque_descricao,
                                  lot.id AS lote_id,
                                  lot.numero AS lote_numero,
                                  f.id AS fornecedor_id,
                                  f.nome AS nome_fornecedor,
                                  CONCAT(lot.numero_nf, '-', lot.serie_nf) AS nota_fiscal_numero,
                                  CAST('Perda' AS TEXT) AS operacao,
                                  CAST(1 AS NUMERIC) AS tipo_movimento_id,
                                  per.quantidade AS quantidade_movimento,
                                  per.unidade_medida_id,
                                  un.abreviacao AS unidade_medida_abreviacao,
                                  u.id AS operador_id,
                                  CONCAT(u.nome, ' ', u.sobrenome) AS operador_nome,
                                  (per.quantidade * p.valor_custo) AS custo_movimentacao
                             FROM perdas per
                                  JOIN usuarios u on U.id = PER.usuario_id
                                  JOIN lotes lot ON lot.id = per.lote_id
                                  JOIN locais_estoque le ON le.id = per.local_estoque_id
                                  JOIN fornecedores f on f.id = lot.fornecedor_id
                                  JOIN produtos p on p.id = lot.produto_id
                                  JOIN sub_grupos s on s.id = p.sub_grupo_id
                                  JOIN produto_grupos g on g.id = s.grupo_id
                                  JOIN unidades_medida un ON un.id = per.unidade_medida_id
                            WHERE lot.group_tenant_id = @GroupTenantId 

                           UNION ALL

                           SELECT eas.id,
                                  eas.data_lancamento AS data_movimentacao,
                                  p.id AS produto_id,
                                  p.descricao AS produto_descricao,
                                  g.id AS grupo_id,
                                  s.id AS subgrupo_id,
                                  ele.id AS local_estoque_id,
                                  ele.descricao AS local_estoque_descricao,
                                  lot.id AS lote_id,
                                  lot.numero AS lote_numero,
                                  f.id AS fornecedor_id,
                                  f.nome AS nome_fornecedor,
                                  CONCAT(lot.numero_nf, '-', lot.serie_nf) AS nota_fiscal_numero,
                                  CAST('Ajuste de Saldo' AS TEXT) AS operacao,
                                  eme.tipo_operacao AS tipo_movimento_id,
                                  eas.quantidade_do_ajuste AS quantidade_movimento,
                                  eas.unidade_medida_id,
                                  un.abreviacao AS unidade_medida_abreviacao,
                                  u.id AS operador_id,
                                  CONCAT(u.nome, ' ', u.sobrenome) AS operador_nome,
                                  (eas.quantidade_do_ajuste * p.valor_custo) AS custo_movimentacao
                             FROM ajuste_saldo_estoque eas
                                  JOIN usuarios u on u.id = eas.operador_id
                                  JOIN lotes lot ON lot.id = eas.lote_id
                                  JOIN locais_estoque ele ON ele.id = eas.local_estoque_id
                                  JOIN fornecedores f on f.id = lot.fornecedor_id
                                  JOIN produtos p on p.id = lot.produto_id
                                  JOIN sub_grupos s on s.id = p.sub_grupo_id
                                  JOIN produto_grupos g on g.id = s.grupo_id
                                  JOIN movimentos_estoque eme ON eme.id = eas.movimento_estoque_id
                                  JOIN unidades_medida un ON un.id = eas.unidade_medida_id
                            WHERE lot.group_tenant_id = @GroupTenantId 

                           UNION ALL    
                              
                           SELECT lot.id,
                                  lot.data_lancamento AS data_movimentacao,
                                  p.id AS produto_id,
                                  p.descricao AS produto_descricao,
                                  g.id AS grupo_id,
                                  s.id AS subgrupo_id,
                                  le.id AS local_estoque_id,
                                  le.descricao AS local_estoque_descricao,
                                  lot.id AS lote_id,
                                  lot.numero AS lote_numero,
                                  f.id AS fornecedor_id,
                                  f.nome AS nome_fornecedor,
                                  CONCAT(lot.numero_nf, '-', lot.serie_nf) AS nota_fiscal_numero,
                                  CAST('Compra' AS TEXT) AS operacao,
                                  CAST(0 AS NUMERIC) AS tipo_movimento_id,
                                  nfel.quantidade AS quantidade_movimento,
                                  nfel.unidade_id AS unidade_medida_id,
                                  um.abreviacao AS unidade_medida_abreviacao,
                                  u.id AS operador_id,
                                  CONCAT(u.nome, ' ', u.sobrenome) AS operador_nome,
                                  (nfei.quantidade_comprada * nfei.valor_unitario) AS custo_movimentacao
                             FROM lotes lot
                                  JOIN fornecedores f on f.id = lot.fornecedor_id
                                  JOIN produtos p ON p.id = lot.produto_id
                                  JOIN sub_grupos s on s.id = p.sub_grupo_id
                                  JOIN produto_grupos g on g.id = s.grupo_id
                                  JOIN notas_fiscais_entrada_lotes nfel ON nfel.numero_lote = lot.numero
                                  JOIN notas_fiscais_entrada_itens nfei ON nfei.id = nfel.nota_fiscal_entrada_item_id
                                  JOIN notas_fiscais_entrada nfe ON nfe.id = nfei.nota_fiscal_entrada_id 
                                  AND nfe.numero = lot.numero_nf AND nfe.serie = lot.serie_nf
                                  JOIN unidades_medida um ON um.id = nfel.unidade_id
                                  JOIN locais_estoque le ON le.id = nfel.local_estoque_id
                                  LEFT JOIN notas_fiscais_historico nfh on nfh.nota_fiscal_entrada_id = nfe.id and nfh.status_alterado = 2
                                  LEFT JOIN usuarios u ON u.id = nfh.usuario_id
                            WHERE lot.group_tenant_id = @GroupTenantId 

                           UNION ALL

                           SELECT rm.id,
                                  rm.data_emissao AS data_movimentacao,
                                  p.id AS produto_id,
                                  p.descricao AS produto_descricao,
                                  g.id AS grupo_id,
                                  s.id AS subgrupo_id,
                                  le.id AS local_estoque_id,
                                  le.descricao AS local_estoque_descricao,
                                  l.id AS lote_id,
                                  l.numero AS lote_numero,
                                  f.id AS fornecedor_id,
                                  f.nome AS nome_fornecedor,
                                  CONCAT(l.numero_nf, '-', l.serie_nf) AS nota_fiscal_numero,
                                  CAST('Receita Manipulada' AS TEXT) AS operacao,
                                  CAST(1 AS NUMERIC) AS tipo_movimento_id,
                                  rmc.quantidade_calculada AS quantidade_movimento,
                                  rmc.unidade_medida_calculada_id AS unidade_medida_id,
                                  um.descricao AS unidade_medida_abreviacao,
                                  u.id AS operador_id,
                                  CONCAT(u.nome, ' ', u.sobrenome) AS operador_nome,
                                  (rmc.quantidade_calculada * p.valor_custo) AS custo_movimentacao
                             FROM receitas_manipuladas rm
                                  JOIN receitas_manipuladas_item rmi on rmi.receita_manipulada_id = rm.id
                                  JOIN formas_farmaceutica ff on ff.id = rm.forma_farmaceutica_id
                                  JOIN laboratorios l2 on l2.id = ff.laboratorio_id
                                  JOIN locais_estoque le on le.id = l2.local_estoque_id
                                  JOIN receita_manipulada_rastreio_calculos rmrc on rmrc.receita_manipulada_id = rm.id and rmrc.receita_manipulada_item_id = rmi.id
                                  JOIN receitas_manipuladas_calculos rmc on rmc.receita_manipulada_rastreio_calculo_id = rmrc.id and rmc.receita_manipulada_id = rmrc.receita_manipulada_id
                                  JOIN unidades_medida um on um.id = rmc.unidade_medida_calculada_id
                                  JOIN lotes l on l.id = rmrc.lote_id
                                  JOIN fornecedores f on f.id = l.fornecedor_id
                                  JOIN produtos p ON p.id = l.produto_id
                                  JOIN sub_grupos s on s.id = p.sub_grupo_id
                                  JOIN produto_grupos g on g.id = s.grupo_id
                                  LEFT JOIN receitas_manipuladas_historico rmh on rmh.receita_manipulada_id = rm.id and rmh.status = 1
                                  LEFT JOIN usuarios u on u.id = rmh.usuario_id
                            WHERE l.group_tenant_id = @GroupTenantId 

                           UNION ALL

                           SELECT rm.id,
                                  rm.data_emissao AS data_movimentacao,
                                  p.id AS produto_id,
                                  p.descricao AS produto_descricao,
                                  g.id AS grupo_id,
                                  s.id AS subgrupo_id,
                                  le.id AS local_estoque_id,
                                  le.descricao AS local_estoque_descricao,
                                  l.id AS lote_id,
                                  l.numero AS lote_numero,
                                  f.id AS fornecedor_id,
                                  f.nome AS nome_fornecedor,
                                  CONCAT(l.numero_nf, '-', l.serie_nf) AS nota_fiscal_numero,
                                  CAST('Produção Interna' AS TEXT) AS operacao,
                                  CAST(1 AS NUMERIC) AS tipo_movimento_id,
                                  rmc.quantidade_calculada AS quantidade_movimento,
                                  rmc.unidade_medida_calculada_id AS unidade_medida_id,
                                  um.descricao AS unidade_medida_abreviacao,
                                  u.id AS operador_id,
                                  CONCAT(u.nome, ' ', u.sobrenome) AS operador_nome,
                                  (rmc.quantidade_calculada * p.valor_custo) AS custo_movimentacao
                             FROM receitas_manipuladas rm
                                  JOIN receitas_manipuladas_item rmi on rmi.receita_manipulada_id = rm.id
                                  JOIN formas_farmaceutica ff on ff.id = rm.forma_farmaceutica_id
                                  JOIN laboratorios l2 on l2.id = ff.laboratorio_id
                                  JOIN locais_estoque le on le.id = l2.local_estoque_id
                                  JOIN receita_manipulada_rastreio_calculos rmrc on rmrc.receita_manipulada_id = rm.id and rmrc.receita_manipulada_item_id = rmi.id
                                  JOIN receitas_manipuladas_calculos rmc on rmc.receita_manipulada_rastreio_calculo_id = rmrc.id and rmc.receita_manipulada_id = rmrc.receita_manipulada_id
                                  JOIN unidades_medida um on um.id = rmc.unidade_medida_calculada_id
                                  JOIN lotes l on l.id = rmrc.lote_id
                                  JOIN fornecedores f on f.id = l.fornecedor_id
                                  JOIN produtos p ON p.id = l.produto_id                                  
                                  JOIN sub_grupos s on s.id = p.sub_grupo_id
                                  JOIN produto_grupos g on g.id = s.grupo_id
                                  LEFT JOIN receitas_manipuladas_historico rmh on rmh.receita_manipulada_id = rm.id and rmh.status = 1
                                  LEFT JOIN usuarios u on u.id = rmh.usuario_id
                            WHERE l.group_tenant_id = @GroupTenantId 

                           UNION ALL

                           SELECT lot.id,
                                  lot.data_lancamento AS data_movimentacao,
                                  p.id AS produto_id,
                                  p.descricao AS produto_descricao,
                                  g.id AS grupo_id,
                                  s.id AS subgrupo_id,
                                  le.id AS local_estoque_id,
                                  le.descricao AS local_estoque_descricao,
                                  lot.id AS lote_id,
                                  lot.numero AS lote_numero,
                                  f.id AS fornecedor_id,
                                  f.nome AS nome_fornecedor,
                                  CONCAT(lot.numero_nf, '-', lot.serie_nf) AS nota_fiscal_numero,
                                  CAST('Venda' AS TEXT) AS operacao,
                                  CAST(1 AS NUMERIC) AS tipo_movimento_id,
                                  nfel.quantidade AS quantidade_movimento,
                                  nfel.unidade_id AS unidade_medida_id,
                                  um.abreviacao AS unidade_medida_abreviacao,
                                  u.id AS operador_id,
                                  CONCAT(u.nome, ' ', u.sobrenome) AS operador_nome,
                                  (nfei.quantidade_comprada * nfei.valor_unitario) AS custo_movimentacao
                             FROM lotes lot
                                  JOIN fornecedores f on f.id = lot.fornecedor_id
                                  JOIN produtos p ON p.id = lot.produto_id
                                  JOIN sub_grupos s on s.id = p.sub_grupo_id
                                  JOIN produto_grupos g on g.id = s.grupo_id
                                  JOIN notas_fiscais_entrada_lotes nfel on nfel.numero_lote = lot.numero
                                  JOIN notas_fiscais_entrada_itens nfei on nfei.id = nfel.nota_fiscal_entrada_item_id
                                  JOIN notas_fiscais_entrada nfe on nfe.id = nfei.nota_fiscal_entrada_id and nfe.numero = lot.numero_nf and nfe.serie = lot.serie_nf
                                  JOIN unidades_medida um on um.id = nfel.unidade_id
                                  LEFT JOIN notas_fiscais_historico nfh on nfh.nota_fiscal_entrada_id = nfe.id and nfh.status_alterado = 2
                                  LEFT JOIN usuarios u ON u.id = nfh.usuario_id
                                  JOIN locais_estoque le on le.id = nfel.local_estoque_id
                            WHERE lot.group_tenant_id = @GroupTenantId
                            
                           UNION ALL

                            SELECT inv.id,
                                   invh.data AS data_movimentacao,
                                   p.id AS produto_id,
                                   p.descricao AS produto_descricao,
                                   g.id AS grupo_id,
                                   sg.id AS subgrupo_id,
                                   lc.id AS local_estoque_id,
                                   lc.descricao AS local_estoque_descricao,
                                   l.id AS lote_id,
                                   l.numero AS lote_numero,
                                   forn.id AS fornecedor_id,
                                   forn.nome AS nome_fornecedor,
                                   CONCAT(l.numero_nf, '-', l.serie_nf) AS nota_fiscal_numero,
                                   CAST('Inventário' AS TEXT) AS operacao,
                                   CASE 
                                       WHEN invi.diferenca < 0 THEN 1
                                       ELSE 0    
                                   END AS tipo_movimento_id,
                                   invi.quantidade_inventariada AS quantidade_movimento,
                                   invi.unidade_medida_id,
                                   um.abreviacao AS unidade_medida_abreviacao,
                                   usr.id AS operador_id,
                                   CONCAT(usr.nome, ' ', usr.sobrenome) AS operador_nome,
                                   (invi.quantidade_inventariada * p.valor_custo) AS custo_movimentacao
                              FROM inventarios inv
                                   JOIN inventario_lancamentos invl on invl.inventario_id = inv.id and invl.codigo_sequencia = inv.codigo_ultimo_lancamento
                                   JOIN inventario_itens invi on invi.inventario_lancamento_id = invl.id and invi.diferenca != 0
                                   JOIN inventario_historicos invh on invh.inventario_lancamento_id = invl.id and invh.status = 4
                                   JOIN lotes l on l.id = invi.lote_id
                                   JOIN produtos p on p.id = l.produto_id
                                   JOIN sub_grupos sg on sg.id = p.sub_grupo_id
                                   JOIN produto_grupos g on g.id = sg.grupo_id
                                   JOIN fornecedores forn on forn.id = l.fornecedor_id
                                   JOIN unidades_medida um on um.id = invi.unidade_medida_id
                                   JOIN usuarios usr on usr.id = inv.responsavel_id
                                   JOIN locais_estoque lc on lc.id = invi.local_estoque_id
                             WHERE inv.group_tenant_id = @GroupTenantId )

                           SELECT id,
                              	 data_movimentacao,
                              	 produto_id,
                                  produto_descricao,
                                  grupo_id,
                                  subgrupo_id,
                                  local_estoque_id,
                              	 local_estoque_descricao,
                                  lote_id,
                              	 lote_numero,
                              	 fornecedor_id,
                                  nome_fornecedor,
                                  nota_fiscal_numero,
                              	 operacao,
                              	 tipo_movimento_id,
                                	 quantidade_movimento,
                                	 unidade_medida_id,
                                  unidade_medida_abreviacao,
                                  operador_id,
                                  operador_nome,
                              	 custo_movimentacao
                             FROM MOVIMENTO_ESTOQUE
                            WHERE 1 = 1
                                  !@SEARCH_CONDITION@!
                           """;

        var conditionalProdutoIds = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "produto_id = ANY ( @ProdutoIds )",
            Predicate = filter => filter.ProdutoIds != null
        };

        var conditionalGrupoIds = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "grupo_id = ANY ( @GrupoIds )",
            Predicate = filter => filter.GrupoIds != null
        };

        var conditionalSubGrupoIds = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "subgrupo_id = ANY ( @SubGrupoIds )",
            Predicate = filter => filter.SubGrupoIds != null
        };

        var conditionalLocalEstoqueIds = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "local_estoque_id = ANY ( @LocalEstoqueIds )",
            Predicate = filter => filter.LocalEstoqueIds != null
        };

        var conditionalFornecedorIds = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "fornecedor_id = ANY ( @FornecedorIds )",
            Predicate = filter => filter.FornecedorIds != null
        };

        var conditionalOperadorIds = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "operador_id = ANY ( @OperadorIds )",
            Predicate = filter => filter.OperadorIds != null
        };

        var conditionalOperacao = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "operacao = ANY ( @Operacao )",
            Predicate = filter => filter.Operacao?.Any() == true
        };

        var conditionalTipoMovimentacao = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "tipo_movimento_id = ANY ( @TipoMovimentacao )",
            Predicate = filter => filter.TipoMovimentacao != null
        };

        var conditionalQuantidadeMovimentada = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "quantidade_movimento >= @QuantidadeMovimentada",
            Predicate = filter => filter.QuantidadeMovimentada.HasValue
        };

        var conditionalCustoMovimentacao = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "custo_movimentacao >= @CustoMovimentacao",
            Predicate = filter => filter.CustoMovimentacao.HasValue
        };

        var conditionalDataLancamentoInicial = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "data_movimentacao >= @DataLancamentoInicial",
            Predicate = filter => filter.DataLancamentoInicial.HasValue
        };

        var conditionalDataLancamentoFinal = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "data_movimentacao <= @DataLancamentoFinal",
            Predicate = filter => filter.DataLancamentoFinal.HasValue
        };

        var conditionalNumerosLote = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "lote_numero = ANY ( @NumerosLote )",
            Predicate = filter => filter.NumerosLote?.Count > 0
        };

        var conditionalNumeroNf = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter = "nota_fiscal_numero = ANY ( @NotaFiscalEntradaNumero )",
            Predicate = filter => filter.NotaFiscalEntradaNumero?.Count > 0
        };

        var searchDataLancamento = new DateSearchField
        {
            Field = "data_movimentacao",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };

        var searchProdutoDescricao = new StringSearchField
        {
            Field = "produto_descricao",
            CompareType = StringCompareType.Contains
        };

        var searchLocalEstoqueDescricao = new StringSearchField
        {
            Field = "local_estoque_descricao",
            CompareType = StringCompareType.Contains
        };

        var searchLoteNumero = new StringSearchField
        {
            Field = "lote_numero",
            CompareType = StringCompareType.Contains
        };

        var searchNomeFornecedor = new StringSearchField
        {
            Field = "nome_fornecedor",
            CompareType = StringCompareType.Contains
        };

        var searchNotaFiscalNumero = new NumberSearchField
        {
            Field = "nota_fiscal_numero",
            CompareType = NumericCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarDetalhadoRequest, ListarDetalhadoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchDataLancamento)
            .AddSearchField(searchProdutoDescricao)
            .AddSearchField(searchLoteNumero)
            .AddSearchField(searchNomeFornecedor)
            .AddSearchField(searchNotaFiscalNumero)
            .AddSearchField(searchLocalEstoqueDescricao)
            .AddFilter(conditionalProdutoIds)
            .AddFilter(conditionalGrupoIds)
            .AddFilter(conditionalSubGrupoIds)
            .AddFilter(conditionalLocalEstoqueIds)
            .AddFilter(conditionalQuantidadeMovimentada)
            .AddFilter(conditionalDataLancamentoInicial)
            .AddFilter(conditionalDataLancamentoFinal)
            .AddFilter(conditionalNumerosLote)
            .AddFilter(conditionalFornecedorIds)
            .AddFilter(conditionalOperadorIds)
            .AddFilter(conditionalOperacao)
            .AddFilter(conditionalTipoMovimentacao)
            .AddFilter(conditionalCustoMovimentacao)
            .AddFilter(conditionalNumeroNf)
            .ExecuteAsync();
    }
}