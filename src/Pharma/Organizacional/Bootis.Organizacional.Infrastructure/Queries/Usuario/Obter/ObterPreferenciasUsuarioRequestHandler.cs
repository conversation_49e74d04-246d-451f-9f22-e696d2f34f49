using System.Data;
using Bootis.Organizacional.Application.Dtos;
using Bootis.Organizacional.Application.Requests.Usuario.Obter;
using Bootis.Organizacional.Domain.Enumerations;
using Bootis.Organizacional.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Infrastructure.Extensions;
using MediatR;

namespace Bootis.Organizacional.Infrastructure.Queries.Usuario.Obter;

public class
    ObterPreferenciasUsuarioRequestHandler(
        IUserContext userContext,
        IDbConnection connection)
    : IRequestHandler<ObterPreferenciasUsuarioRequest,
        ObterPreferenciasUsuarioResponse>
{
    public async Task<ObterPreferenciasUsuarioResponse> Handle(ObterPreferenciasUsuarioRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT u.id AS id,
                                  t.tema_usuario AS tema_usuario,
                                  t.texto_negrito AS texto_negrito,
                                  t.texto_ampliado AS texto_ampliado,
                                  t.contraste_aumentado AS contraste_aumentado,
                                  t.time_zone AS time_zone,
                                  t.idioma AS idioma,
                                  t.padrao_data AS padrao_data,
                                  t.padrao_hora AS padrao_hora
                           FROM usuarios u
                                    LEFT JOIN preferencias_usuario t ON t.id = u.id
                           WHERE u.id = @userId
                           """;

        var result =
            await connection.QueryListAsync<QueryResponse>(sql,
                new { userContext.UserId });

        if (result.Count != 0)
            return MapToObterPreferenciasUsuarioLogadoResponse(result);

        var message =
            Localizer.Instance.GetMessage_Usuario_GuidNaoEncontrado(userContext.UserId);
        throw new DomainException(message);
    }

    private static ObterPreferenciasUsuarioResponse MapToObterPreferenciasUsuarioLogadoResponse(
        List<QueryResponse> result)
    {
        var response = new ObterPreferenciasUsuarioResponse
        {
            Id = result[0].Id
        };

        PreferenciaUsuarioBaseDto preferencias;

        if (result[0].TemaUsuario != null)
            preferencias = new PreferenciaUsuarioBaseDto
            {
                TemaUsuario = result[0].TemaUsuario.Value,
                TextoNegrito = result[0].TextoNegrito,
                TextoAmpliado = result[0].TextoAmpliado,
                ContrasteAumentado = result[0].ContrasteAumentado,
                TimeZone = result[0].TimeZone,
                Idioma = result[0].Idioma,
                PadraoData = result[0].PadraoData,
                PadraoHora = result[0].PadraoHora
            };
        else
            preferencias = new PreferenciaUsuarioBaseDto();

        response.Preferencias = preferencias;

        return response;
    }

    private record QueryResponse
    {
        public Guid Id { get; init; }
        public TipoTemaUsuario? TemaUsuario { get; init; }
        public bool TextoNegrito { get; init; }
        public bool TextoAmpliado { get; init; }
        public bool ContrasteAumentado { get; init; }
        public string TimeZone { get; init; }
        public string Idioma { get; init; }
        public string PadraoData { get; init; }
        public string PadraoHora { get; init; }
    }
}