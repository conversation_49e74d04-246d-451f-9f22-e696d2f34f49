using System.Data;
using Bootis.Organizacional.Application.Dtos;
using Bootis.Organizacional.Application.Helpers;
using Bootis.Organizacional.Application.Requests.Usuario.Obter;
using Bootis.Organizacional.Domain.Enumerations;
using Bootis.Organizacional.Domain.Statics;
using Bootis.Organizacional.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Infrastructure.Extensions;
using MediatR;

namespace Bootis.Organizacional.Infrastructure.Queries.Usuario.Obter;

public class ObterUsuarioRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ObterUsuarioRequest, ObterUsuarioResponse>
{
    public async Task<ObterUsuarioResponse> Handle(ObterUsuarioRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                              SELECT DISTINCT u.id AS id,
                                           u.id AS id,
                                           u.nome AS nome,
                                           u.sobrenome AS sobrenome,
                                           u.cpf AS cpf,
                                           u.data_nascimento AS data_nascimento,
                                           u.email AS email,
                                           u.ativo AS ativo,
                                           u.email_alternativo AS email_alternativo,
                                           u.tenant_id AS tenant_id,
                                           t.numero AS celular,
                                           g.id AS grupo_id,
                                           pu.permissao_id AS permissao_usuario_id,
                                           pu.ativo AS permissao_usuario_ativa,
                                           gp.permissao_id AS grupo_permissao_id,
                                           r.tema_usuario AS tema_usuario,
                                           r.texto_negrito AS texto_negrito,
                                           r.texto_ampliado AS texto_ampliado,
                                           r.contraste_aumentado AS contraste_aumentado,
                                           r.time_zone AS time_zone,
                                           r.idioma AS idioma,
                                           r.padrao_data AS padrao_data,
                                           r.padrao_hora AS padrao_hora
                           FROM usuarios u
                                    LEFT JOIN telefone_usuario t ON t.id = u.id
                                    LEFT JOIN usuarios_grupos ug ON ug.usuarios_id = u.id
                                    LEFT JOIN grupos g ON g.id = ug.grupos_id
                                    LEFT JOIN permissoes_usuarios pu ON pu.usuario_id = u.id
                                    LEFT JOIN preferencias_usuario r ON r.id = u.id
                                    LEFT JOIN grupos_permissoes gp ON gp.grupo_id = ug.grupos_id 
                                        AND NOT EXISTS (SELECT 1
                                                        FROM permissoes_usuarios
                                                        WHERE permissao_id = gp.permissao_id
                                                          AND usuario_id = u.id)
                           WHERE u.id = @userId
                           """;

        var result =
            await connection.QueryListAsync<QueryResponse>(sql,
                new { userContext.UserId });

        if (result.Count != 0)
            return MapToObterUsuarioLogadoResponse(result);

        var message =
            Localizer.Instance.GetMessage_Usuario_GuidNaoEncontrado(userContext.UserId);
        throw new DomainException(message);
    }

    private static ObterUsuarioResponse MapToObterUsuarioLogadoResponse(List<QueryResponse> result)
    {
        var usuario = new ObterUsuarioResponse
        {
            Id = result[0].Id,
            Sobrenome = result[0].Sobrenome,
            Cpf = result[0].Cpf,
            DataNascimento = result[0].DataNascimento,
            Email = result[0].Email,
            EmailAlternativo = result[0].EmailAlternativo,
            Nome = result[0].Nome,
            Celular = result[0].Celular,
            Permissoes = new List<PermissaoUsuarioDto>(),
            Grupos = new List<Guid>(),
            ModulosSistema = SystemModulesHelper.GetSystemModule(result[0].TenantId)
        };

        if (result[0].TemaUsuario is not null)
            usuario.Preferencias = new PreferenciaUsuarioBaseDto
            {
                TemaUsuario = result[0].TemaUsuario.Value,
                TextoNegrito = result[0].TextoNegrito,
                TextoAmpliado = result[0].TextoAmpliado,
                ContrasteAumentado = result[0].ContrasteAumentado,
                TimeZone = result[0].TimeZone,
                Idioma = result[0].Idioma,
                PadraoData = result[0].PadraoData,
                PadraoHora = result[0].PadraoHora
            };

        foreach (var item in result)
        {
            if (item.GrupoId != null && usuario.Grupos.All(g => g != item.GrupoId))
                usuario.Grupos.Add(item.GrupoId.Value);

            SetPermissaoUsuario(item, usuario);

            SetPermissaoGrupo(item, usuario);
        }

        return usuario;
    }

    private static void SetPermissaoGrupo(QueryResponse item, ObterUsuarioResponse usuario)
    {
        if (item.GrupoPermissaoId is null) return;
        var permissao = PermissoesStatic.GetPermissao(item.GrupoPermissaoId.Value);
        if (permissao is null) return;
        usuario.Permissoes.Add(new PermissaoUsuarioDto
        {
            Ativo = true,
            Herdado = true,
            PermissaoId = permissao.Id
        });
    }

    private static void SetPermissaoUsuario(QueryResponse item, ObterUsuarioResponse usuario)
    {
        if (item.PermissaoUsuarioId is null) return;
        var permissao = PermissoesStatic.GetPermissao(item.PermissaoUsuarioId.Value);
        if (permissao is null) return;
        usuario.Permissoes.Add(new PermissaoUsuarioDto
        {
            Ativo = item.PermissaoUsuarioAtiva,
            Herdado = false,
            PermissaoId = permissao.Id
        });
    }

    private record QueryResponse
    {
        public Guid Id { get; init; }
        public string Nome { get; init; }
        public string Sobrenome { get; init; }
        public string Cpf { get; init; }
        public DateTime DataNascimento { get; init; }
        public string Email { get; init; }
        public bool Ativo { get; init; }
        public string EmailAlternativo { get; init; }
        public string Celular { get; init; }
        public Guid? GrupoId { get; init; }
        public int? PermissaoUsuarioId { get; init; }
        public bool PermissaoUsuarioAtiva { get; init; }
        public int? GrupoPermissaoId { get; init; }
        public TipoTemaUsuario? TemaUsuario { get; init; }
        public bool TextoNegrito { get; init; }
        public bool TextoAmpliado { get; init; }
        public bool ContrasteAumentado { get; init; }
        public string TimeZone { get; init; }
        public string Idioma { get; init; }
        public string PadraoData { get; init; }
        public string PadraoHora { get; init; }
        public Guid TenantId { get; set; }
    }
}