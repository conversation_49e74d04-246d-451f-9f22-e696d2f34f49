using System.Data;
using Bootis.Organizacional.Application.Requests.Moeda.Listar;
using Dapper;
using MediatR;

namespace Bootis.Organizacional.Infrastructure.Queries.Moeda.Listar;

public class ListarRequestHandler(
    IDbConnection connection) : IRequestHandler<ListarRequest, ListarResponse>
{
    public async Task<ListarResponse> Handle(ListarRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT 
                               m.name,
                               m.id
                             FROM tipos_moeda m
                           """;

        var moedas = await connection.QueryAsync<ListarResponse.TipoMoeda>(sql);

        return new ListarResponse
        {
            Moedas = moedas
        };
    }
}