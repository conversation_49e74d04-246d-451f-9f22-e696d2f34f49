using System.Data;
using Bootis.Organizacional.Application.Requests.Conglomerado.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Organizacional.Infrastructure.Queries.Conglomerado.Listar;

public class ListarDetalhadoAdminRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarDetalhadoAdminRequest,
        PaginatedResult<ListarDetalhadoAdminResponse>>
{
    public Task<PaginatedResult<ListarDetalhadoAdminResponse>> Handle(ListarDetalhadoAdminRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT 
                                  con.nome,
                                  con.id,
                                  emp.nome_fantasia AS matriz,
                                  COUNT(emps.id) AS empresas,
                                  con.data_inclusao,
                                  con.ativo
                           FROM conglomerados con
                                LEFT JOIN conglomerado_matriz cmat ON
                                          cmat.id = con.id
                                LEFT JOIN empresas emp ON
                                          emp.id = cmat.empresa_id
                                LEFT JOIN empresas emps ON
                                          emps.conglomerado_id = con.id
                           WHERE 1 = 1
                                 !@SEARCH_CONDITION@!
                           GROUP BY con.id,
                                    con.nome,
                                    emp.nome_fantasia,
                                    con.data_inclusao,
                                    con.ativo
                           """;

        var searchNomeConglomerado = new StringSearchField
        {
            Field = "CON.nome",
            CompareType = StringCompareType.Contains
        };

        var searchDataInclucao = new DateSearchField
        {
            Field = "CON.data_inclusao",
            CompareType = DateCompareType.Contains,
            DateFormat = userContext.UserSession.UserPreferences.DateFormat
        };

        var searchEmpresaMatriz = new StringSearchField
        {
            Field = "EMP.nome_fantasia",
            CompareType = StringCompareType.Contains
        };

        var searchAtivo = new BooleanSearchField
        {
            Field = "CON.ativo",
            True = "ativo",
            False = "inativo"
        };

        return PaginatedQueryBuilder<ListarDetalhadoAdminRequest, ListarDetalhadoAdminResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchNomeConglomerado)
            .AddSearchField(searchDataInclucao)
            .AddSearchField(searchEmpresaMatriz)
            .AddSearchField(searchAtivo)
            .ExecuteAsync();
    }
}