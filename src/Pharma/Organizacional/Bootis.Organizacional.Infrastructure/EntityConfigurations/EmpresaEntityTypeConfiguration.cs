using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Organizacional.Domain.Enumerations;
using Bootis.Organizacional.Infrastructure.Helpers;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Organizacional.Infrastructure.EntityConfigurations;

public class EmpresaEntityTypeConfiguration : BaseEntityTypeConfiguration<Empresa>
{
    public override void Configure(EntityTypeBuilder<Empresa> builder)
    {
        builder.ToTable("empresas");

        builder
            .Property(c => c.RazaoSocial)
            .NomeDescricao(TamanhoTexto.Mil)
            .IsRequired();

        builder
            .Property(c => c.NomeFantasia)
            .NomeDescricao(TamanhoTexto.Mil)
            .IsRequired();

        builder
            .Property(c => c.Cnpj)
            .Documento(TamanhoTexto.Vinte)
            .IsRequired();

        builder
            .Property(c => c.Cnae)
            .Documento(TamanhoTexto.Dez)
            .IsRequired();

        builder
            .Property(c => c.InscricaoEstadual)
            .Documento(TamanhoTexto.Vinte)
            .IsRequired();

        builder
            .Property(c => c.InscricaoMunicipal)
            .Documento(TamanhoTexto.Vinte)
            .IsRequired(false);

        builder.HasOne<TipoEmpresa>()
            .WithMany()
            .HasForeignKey(c => c.TipoId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder.HasOne(c => c.Conglomerado)
            .WithMany(c => c.Empresas)
            .HasForeignKey(c => c.ConglomeradoId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .OwnsOne(c => c.Endereco,
                EntityConfigurationHelper
                    .GetEnderecoBuildAction<Empresa>("endereco_empresa")); //UNDONE: Rever relacionamento

        builder
            .HasOne(c => c.EmpresaPagadora)
            .WithMany()
            .HasForeignKey(c => c.EmpresaPagadoraId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .OwnsMany(b => b.Telefones,
                EntityConfigurationHelper
                    .GetTelefoneBuildAction<Empresa>("telefones_empresa")); //UNDONE: Rever relacionamento

        builder
            .OwnsOne(b => b.Configuracao,
                EntityConfigurationHelper
                    .GetConfiguracaoBuildAction<Empresa>("configuracoes_empresa")); //UNDONE: Rever relacionamento

        builder
            .Property(c => c.Email)
            .Web()
            .HasColumnType("varchar")
            .IsRequired(false);

        builder
            .Property(c => c.Site)
            .Web()
            .HasColumnType("varchar")
            .IsRequired(false);

        builder
            .Property(c => c.Ativa)
            .IsRequired()
            .HasDefaultValue(true);

        builder
            .Property(c => c.Telefone)
            .Documento(TamanhoTexto.Vinte)
            .IsRequired(false);

        builder
            .Property(c => c.EmpresaPagadoraId)
            .IsRequired(false);

        builder
            .OwnsOne(b => b.Responsavel,
                b =>
                {
                    b.WithOwner();

                    b.HasOne(c => c.Usuario)
                        .WithMany()
                        .OnDelete(DeleteBehavior.Restrict);

                    b.ToTable("empresa_responsavel");
                });

        base.Configure(builder);
    }
}