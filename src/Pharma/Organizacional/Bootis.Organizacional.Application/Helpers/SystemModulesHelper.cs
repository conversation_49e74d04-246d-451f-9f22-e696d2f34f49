namespace Bootis.Organizacional.Application.Helpers;

public static class SystemModulesHelper
{
    private static readonly Guid BootisId = new("11111111-1111-1111-1111-111111111111");

    private static readonly IEnumerable<string> Admin = new[] { "Admin" };

    private static readonly IEnumerable<string> Normal = new[]
        { "Administrativo", "Estoque", "Pessoas", "Produto", "Compras", "Vendas", "Produção" };

    public static IEnumerable<string> GetSystemModule(Guid tenantId)
    {
        return tenantId == BootisId ? Admin : Normal;
    }
}