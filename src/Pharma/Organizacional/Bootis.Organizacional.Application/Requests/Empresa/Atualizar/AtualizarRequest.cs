using MediatR;

namespace Bootis.Organizacional.Application.Requests.Empresa.Atualizar;

public class AtualizarRequest : IRequest
{
    public Guid Id { get; init; }
    public string RazaoSocial { get; init; }
    public string NomeFantasia { get; init; }
    public string Cnpj { get; init; }
    public string InscricaoEstadual { get; init; }
    public string InscricaoMunicipal { get; init; }
    public Guid? EmpresaPagadoraId { get; init; }
    public string Cnae { get; init; }
    public string Email { get; init; }
    public string Site { get; init; }
    public string Telefone { get; init; }
    public int TipoMoedaId { get; init; }
}