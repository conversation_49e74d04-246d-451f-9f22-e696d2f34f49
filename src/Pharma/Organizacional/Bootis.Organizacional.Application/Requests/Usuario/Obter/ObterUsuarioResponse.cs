using Bootis.Organizacional.Application.Dtos;

namespace Bootis.Organizacional.Application.Requests.Usuario.Obter;

public class ObterUsuarioResponse
{
    public Guid Id { get; set; }
    public string Nome { get; set; }
    public string Sobrenome { get; set; }
    public string Cpf { get; set; }
    public DateTime? DataNascimento { get; set; }
    public string Email { get; set; }
    public string EmailAlternativo { get; set; }
    public string Celular { get; set; }
    public PreferenciaUsuarioBaseDto Preferencias { get; set; }
    public ICollection<Guid> Grupos { get; set; }
    public ICollection<PermissaoUsuarioDto> Permissoes { get; set; }
    public IEnumerable<string> ModulosSistema { get; set; }
}