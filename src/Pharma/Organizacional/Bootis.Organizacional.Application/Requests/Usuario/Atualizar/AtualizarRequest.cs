using Bootis.Organizacional.Domain.Dtos;
using MediatR;

namespace Bootis.Organizacional.Application.Requests.Usuario.Atualizar;

public class AtualizarRequest : IRequest
{
    public Guid Id { get; init; }
    public string Nome { get; init; }
    public string Sobrenome { get; init; }
    public string Cpf { get; init; }
    public string Email { get; init; }
    public DateOnly? DataNascimento { get; init; }
    public string EmailAlternativo { get; init; }
    public string Celular { get; init; }
    public PreferenciaDto Preferencias { get; init; }
    public ICollection<Guid> Grupos { get; init; } = Array.Empty<Guid>();
}