using Bootis.Organizacional.Application.Extensions;
using Bootis.Organizacional.Application.Requests.Empresa.Atualizar;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Organizacional.Application.UseCases.Empresa;

public class AtualizarUsuarioResponsavelRequestHandler(
    IUnitOfWork unitOfWork,
    IEmpresaRepository empresaRepository,
    IUsuarioRepository usuarioRepository)
    : IRequestHandler<AtualizarUsuarioResponsavelRequest>
{
    public async Task Handle(AtualizarUsuarioResponsavelRequest request, CancellationToken cancellationToken)
    {
        var usuario = await usuarioRepository.ObterUsuarioAsync(request.UsuarioResponsavelId);

        foreach (var empresaId in request.EmpresasId)
        {
            var empresa = await empresaRepository.ObterEmpresaAsync(empresaId);

            empresa.AtualizarResponsavel(usuario);

            empresaRepository.Update(empresa);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}