using Bootis.Organizacional.Application.Extensions;
using Bootis.Organizacional.Application.Mappers;
using Bootis.Organizacional.Application.Requests.Conglomerado.Cadastrar;
using Bootis.Organizacional.Domain.AggregatesModel.ConglomeradoAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Organizacional.Domain.Enumerations;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Organizacional.Application.UseCases.Conglomerado;

public class CadastrarRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IConglomeradoRepository conglomeradoRepository,
    IEmpresaRepository empresaRepository,
    IUsuarioRepository usuarioRepository)
    : IRequestHandler<CadastrarRequest>
{
    public async Task Handle(CadastrarRequest request, CancellationToken cancellationToken)
    {
        var endereco = EnderecoMapper.EnderecoFrom(request.Endereco);
        var usuarioResponsavel = await UsuarioResponsavelAsync(request);

        await empresaRepository.ValidarEmpresaAsync(request.Empresa.Cnpj);

        var conglomerado = new Domain.AggregatesModel.ConglomeradoAggregate.Conglomerado(request.Nome,
            request.Empresa.RazaoSocial, request.Empresa.NomeFantasia, request.Empresa.Cnpj,
            endereco, usuarioResponsavel, request.Empresa.Cnae, request.Empresa.InscricaoEstadual,
            request.Empresa.InscricaoMunicipal, request.Empresa.Email, request.Empresa.Site,
            request.Empresa.Telefone);

        var matriz = conglomerado.Matriz.Empresa;

        matriz.Configuracao.AlterarTipoMoeda(matriz.Id, request.Empresa.TipoMoedaId);

        matriz.AtualizarResponsavel(usuarioResponsavel);

        foreach (var telefoneDto in request.Telefones)
        {
            var telefone = TelefoneMapper.TelefoneFrom(telefoneDto);
            matriz.AdicionarTelefone(telefone);
        }

        userContext.UserSession = userContext.UserSession with
        {
            UserIdentity = userContext.UserSession.UserIdentity with
            {
                TenantId = matriz.Id,
                GroupTenantId = conglomerado.Id
            }
        };

        conglomeradoRepository.Add(conglomerado);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }

    private async Task<Domain.AggregatesModel.UsuarioAggregate.Usuario> UsuarioResponsavelAsync(
        CadastrarRequest request)
    {
        await usuarioRepository.ValidarUsuarioEmailAsync(request.Usuario.Email);

        var usuario = new Domain.AggregatesModel.UsuarioAggregate.Usuario(request.Usuario.Nome,
            request.Usuario.Sobrenome, request.Usuario.Email, TipoUsuario.Master.Id, request.Usuario.DataNascimento,
            request.Usuario.Cpf, request.Usuario.Celular);

        return usuario;
    }
}