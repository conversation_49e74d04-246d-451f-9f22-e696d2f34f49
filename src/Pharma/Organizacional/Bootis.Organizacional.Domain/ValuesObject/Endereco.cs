using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Organizacional.Domain.ValuesObject;

public class Endereco(
    string cep,
    string logradouro,
    int? numero,
    string complemento,
    string bairro,
    string cidade,
    string estado)
    : ValueObject
{
    public string Cep { get; } = cep;
    public string Logradouro { get; } = logradouro;
    public string Complemento { get; } = complemento;
    public int? Numero { get; } = numero;
    public string Bairro { get; } = bairro;
    public string Cidade { get; } = cidade;
    public string Estado { get; } = estado;

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Cep;
        yield return Logradouro;
        yield return Numero;
        yield return Complemento;
        yield return Bairro;
        yield return Cidade;
        yield return Estado;
    }
}