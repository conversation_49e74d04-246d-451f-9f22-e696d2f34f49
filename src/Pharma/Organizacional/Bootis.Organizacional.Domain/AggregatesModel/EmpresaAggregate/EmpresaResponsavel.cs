using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;

namespace Bootis.Organizacional.Domain.AggregatesModel.EmpresaAggregate;

public class EmpresaResponsavel
{
    public EmpresaResponsavel()
    {
    }

    public EmpresaResponsavel(Usuario usuario)
    {
        Usuario = usuario;
    }

    #region Navigation properties

    public virtual Usuario Usuario { get; private set; }

    #endregion

    public void DefineUsuario(Usuario usuario)
    {
        Usuario = usuario;
    }
}