using Bootis.Producao.Application.Requests.ReceitaManipulada.Cancelar;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Producao.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Bootis.Producao.Application.UseCases.ReceitaManipulada;

public class CancelarReceitaManipuladaRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IReceitaManipuladaRepository receitaManipuladaRepository,
    ILogger<CancelarReceitaManipuladaRequestHandler> logger)
    : IRequestHandler<CancelarReceitaManipuladaRequest>
{
    public async Task Handle(CancelarReceitaManipuladaRequest request, CancellationToken cancellationToken)
    {
        var receitaManipulada = await receitaManipuladaRepository.ObterReceitaManipuladaPorIdAsync(request.ReceitaManipuladaId)
            ?? throw new ValidationException(
                Localizer.Instance.GetMessage_ReceitaManipulada_GuidNaoEncontrado(request.ReceitaManipuladaId));

        // Check if already cancelled
        if (receitaManipulada.Status is StatusReceita.Cancelado or StatusReceita.ReceitaCancelada)
        {
            logger.LogInformation("Receita manipulada {ReceitaId} já está cancelada. Status atual: {Status}",
                request.ReceitaManipuladaId, receitaManipulada.Status);
            return;
        }

        // Validate motive is provided
        if (string.IsNullOrWhiteSpace(request.MotivoCancelamento))
        {
            throw new ValidationException("O motivo do cancelamento é obrigatório.");
        }

        // Use the domain method that requires motive
        receitaManipulada.CancelarReceita(userContext.UserId, request.MotivoCancelamento);

        receitaManipuladaRepository.Update(receitaManipulada);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        logger.LogInformation("Receita manipulada {ReceitaId} cancelada com sucesso. Motivo: {Motivo}. Usuário: {UserId}",
            request.ReceitaManipuladaId, request.MotivoCancelamento, userContext.UserId);
    }
}
