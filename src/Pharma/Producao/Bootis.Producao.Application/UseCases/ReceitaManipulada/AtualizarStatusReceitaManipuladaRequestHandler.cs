using Bootis.Producao.Application.Requests.ReceitaManipulada.AtualizarStatus;
using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Producao.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Bootis.Producao.Application.UseCases.ReceitaManipulada;

public class AtualizarStatusReceitaManipuladaRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IReceitaManipuladaRepository receitaManipuladaRepository,
    ILogger<AtualizarStatusReceitaManipuladaRequestHandler> logger)
    : IRequestHandler<AtualizarStatusReceitaManipuladaRequest>
{
    public async Task Handle(AtualizarStatusReceitaManipuladaRequest request, CancellationToken cancellationToken)
    {
        var receitaManipulada = await receitaManipuladaRepository.ObterReceitaManipuladaPorIdAsync(request.ReceitaManipuladaId)
            ?? throw new ValidationException(
                Localizer.Instance.GetMessage_ReceitaManipulada_GuidNaoEncontrado(request.ReceitaManipuladaId));

        // Prevent changing to cancellation status - use the dedicated cancellation endpoint instead
        if (request.NovoStatus is StatusReceita.Cancelado or StatusReceita.ReceitaCancelada)
        {
            logger.LogWarning("Tentativa de cancelar receita manipulada {ReceitaId} através do endpoint de atualização de status. Use o endpoint de cancelamento.",
                request.ReceitaManipuladaId);

            throw new ValidationException("Para cancelar uma receita, utilize o endpoint específico de cancelamento que requer um motivo.");
        }

        // Prevent changing to the same status
        if (receitaManipulada.Status == request.NovoStatus)
        {
            logger.LogInformation("Receita manipulada {ReceitaId} já está no status {Status}. Nenhuma alteração necessária.",
                request.ReceitaManipuladaId, request.NovoStatus);
            return;
        }

        receitaManipulada.AtualizarStatus(request.NovoStatus, userContext.UserId);

        receitaManipuladaRepository.Update(receitaManipulada);
        await unitOfWork.SaveChangesAsync(cancellationToken);

        logger.LogInformation("Status da receita manipulada {ReceitaId} atualizado com sucesso de {StatusAnterior} para {NovoStatus}. Usuário: {UserId}",
            request.ReceitaManipuladaId, receitaManipulada.Status, request.NovoStatus, userContext.UserId);
    }
}