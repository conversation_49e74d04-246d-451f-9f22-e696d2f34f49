using Bootis.Producao.Application.Extensions;
using Bootis.Producao.Application.Requests.ModeloOrdemManipulacao.AtualizarStatus;
using Bootis.Producao.Domain.AggregatesModel.ModeloOrdemManipulacaoAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Producao.Application.UseCases.ModeloOrdemManipulacao;

public class AtualizarStatusRequestHandler(
    IUnitOfWork unitOfWork,
    IModeloOrdemManipulacaoRepository modeloOrdemManipulacaoRepository)
    : IRequestHandler<AtualizarStatusRequest>
{
    public async Task Handle(AtualizarStatusRequest request, CancellationToken cancellationToken)
    {
        var modelosOrdemManipulacao = await modeloOrdemManipulacaoRepository
            .ObterModelosOrdemManipulacaoAsync(request.Id);

        foreach (var modelo in modelosOrdemManipulacao)
        {
            modelo.AtualizarStatus(request.Ativo);
            modeloOrdemManipulacaoRepository.Update(modelo);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}