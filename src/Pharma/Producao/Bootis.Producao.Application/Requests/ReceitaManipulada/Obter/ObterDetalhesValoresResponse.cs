namespace Bootis.Producao.Application.Requests.ReceitaManipulada.Obter;

public class ObterDetalhesValoresResponse
{
    public decimal PrecoCustoTotal { get; set; }
    public decimal PrecoVendaTotal { get; set; }
    public decimal PercentualLucroTotal { get; set; }
    public decimal CustoOperacional { get; set; }
    public decimal Lucro { get; set; }
    public decimal PrecoReceita { get; set; }
    public decimal PrecoBruto { get; set; }
    public decimal Desconto { get; set; }
    public List<DetalheValorComponente> Componentes { get; set; }
}

public class DetalheValorComponente
{
    public Guid ComponenteId { get; set; }
    public decimal PrecoCusto { get; set; }
    public decimal PercentualLucro { get; set; }
    public decimal PrecoVenda { get; set; }
}