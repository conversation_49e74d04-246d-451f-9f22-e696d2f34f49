using System.ComponentModel;
using Bootis.Producao.Domain.Enumerations;

namespace Bootis.Producao.Application.Requests.Posologia.Obter;

public class ObterResponse
{
    public Guid Id { get; set; }
    public string PosologiaDescricao { get; set; }
    public Guid FormaFarmaceuticaId { get; set; }
    public string FormaFarmaceuticaDescricao { get; set; }
    public float QuantidadeDosePorPeriodo { get; set; }
    public string UnidadeMedidaAbreviacao { get; set; }
    public PeriodosPosologia PeriodoId { get; set; }

    public virtual string PeriodoDescricao
    {
        get
        {
            var descriptionAttribute = typeof(PeriodosPosologia)
                    .GetField(PeriodoId.ToString())
                    .GetCustomAttributes(typeof(DescriptionAttribute), false)
                as DescriptionAttribute[];

            return descriptionAttribute != null && descriptionAttribute.Length > 0
                ? descriptionAttribute[0].Description
                : PeriodoId.ToString();
        }
    }

    public bool Ativo { get; set; }
}