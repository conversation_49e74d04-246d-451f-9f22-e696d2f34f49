using Bootis.Producao.Application.Requests.ReceitaManipulada.Atualizar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Producao.Application.Validations.ReceitaManipuladaValidation;

public class
    AtualizarReceitaManipuladaRotuloRequestValidator : AbstractValidator<AtualizarReceitaManipuladaRotuloRequest>
{
    public AtualizarReceitaManipuladaRotuloRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.Id)
            .NotEmpty()
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.Id)));

        RuleFor(c => c.<PERSON>eticao)
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.<PERSON>eticao)));
    }
}