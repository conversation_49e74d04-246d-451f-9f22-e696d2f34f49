using Bootis.Producao.Application.Requests.ReceitaManipulada.Cancelar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Producao.Application.Validations.ReceitaManipuladaValidation;

public class CancelarReceitaManipuladaRequestValidator : AbstractValidator<CancelarReceitaManipuladaRequest>
{
    public CancelarReceitaManipuladaRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.ReceitaManipuladaId)
            .NotEmpty()
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.ReceitaManipuladaId)));

        RuleFor(c => c.MotivoCancelamento)
            .NotEmpty()
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.MotivoCancelamento)))
            .MaximumLength(500)
            .WithMessage(l => localizer.GetMessage_Validation_TamanhoMaximo(nameof(l.MotivoCancelamento), 500))
            .MinimumLength(10);
    }
}