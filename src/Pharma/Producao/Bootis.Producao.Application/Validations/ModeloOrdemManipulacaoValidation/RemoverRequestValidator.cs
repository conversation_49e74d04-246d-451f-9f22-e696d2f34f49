using Bootis.Producao.Application.Requests.ModeloOrdemManipulacao.Remover;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Producao.Application.Validations.ModeloOrdemManipulacaoValidation;

public class RemoverRequestValidator : AbstractValidator<RemoverRequest>
{
    public RemoverRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.Id)
            .NotEmpty();
    }
}