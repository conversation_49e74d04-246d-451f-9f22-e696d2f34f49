using Bootis.Producao.Application.Requests.Posologia.Atualizar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Producao.Application.Validations.PosologiaValidation;

public class AtualizarRequestValidator : AbstractValidator<AtualizarRequest>
{
    public AtualizarRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.Id)
            .NotEmpty();

        RuleFor(c => c.Descricao)
            .MaximumLength(300)
            .NotEmpty();

        RuleFor(c => c.FormaFarmaceuticaId)
            .NotEmpty();

        RuleFor(c => c.UnidadeMedidaId)
            .IsInEnum()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.UnidadeMedidaId)));

        RuleFor(c => c.Periodo)
            .IsInEnum()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.Periodo)));

        RuleFor(c => c.QuantidadeDosePorPeriodo)
            .NotEmpty()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.QuantidadeDosePorPeriodo)));
    }
}