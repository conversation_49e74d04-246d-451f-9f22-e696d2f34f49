using Bootis.Producao.Domain.Enumerations;
using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Producao.Domain.AggregatesModel.ModeloOrdemManipulacaoAggregate;

public interface IModeloOrdemManipulacaoRepository : IRepository<ModeloOrdemManipulacao>, IScopedService
{
    Task<ModeloOrdemManipulacao> ObterPorIdAsync(Guid id);
    Task<List<ModeloOrdemManipulacao>> ObterPorIdsAsync(IEnumerable<Guid> ids);
    Task<bool> ValidarDescricaoAsync(string descricao, TipoOrdemManipulacao tipoId);
    Task<bool> ValidarIdEDescricaoAsync(string descricao, TipoOrdemManipulacao tipoId, Guid id);

    Task<IEnumerable<(Guid OrdemManipulacaoId, string ModeloDescricao)>>
        VerificarDependenciasAsync(IEnumerable<Guid> ids);
}