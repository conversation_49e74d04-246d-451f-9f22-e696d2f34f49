using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Producao.Domain.AggregatesModel.LaboratorioAggregate;

public interface ILaboratorioRepository : IRepository<Laboratorio>, IScopedService
{
    Task<Laboratorio> ObterPorIdAsync(Guid id);
    Task<List<Laboratorio>> ObterPorIdsAsync(IEnumerable<Guid> ids);
    Task<bool> ValidarPorEmpresaIdEDescricaoAsync(Guid empresaId, string descricaoLaboratorio);

    Task<bool> ValidarPorIdEEmpresaIdEDescricaoAsync(Guid id, Guid empresaId,
        string descricaoLaboratorio);

    Task<int> VerificarDependeciaLaboratorioAsync(Guid id);

    Task<List<Laboratorio>> ObterLaboratoriosSemModeloOrdemCadastradoAsync(string descricao,
        int tipoOrdemManipulacaoId);
}