using Bootis.Shared.Common.Interfaces;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Producao.Domain.AggregatesModel.PosologiaAggregate;

public interface IPosologiaRepository : IRepository<Posologia>, IScopedService
{
    Task<Posologia> ObterPorIdAsync(Guid id);
    Task<List<Posologia>> ObterPorIdsAsync(IEnumerable<Guid> ids);
    Task<bool> ValidarPorFormaFarmaceuticaIdEDescricaoAsync(Guid formaFarmaceuticaId, string descricao);

    Task<bool> ValidarPorIdEFormaFarmaceuticaIdEDescricaoAsync(Guid id, Guid formaFarmaceuticaId,
        string descricao);

    Task<int> VerificarDependenciaAsync(Guid id);
}