using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Producao.Domain.Enumerations;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;

public class ReceitaManipuladaHistorico : Entity
{
    protected ReceitaManipuladaHistorico()
    {
    }

    public ReceitaManipuladaHistorico(Guid usuarioId,
        ReceitaManipulada receitaManipulada,
        StatusReceita status) : this()
    {
        UsuarioId = usuarioId;
        ReceitaManipulada = receitaManipulada;
        Status = status;
        DataHora = DateTime.UtcNow;
    }

    public ReceitaManipuladaHistorico(Guid usuarioId,
        ReceitaManipulada receitaManipulada,
        StatusReceita status,
        string motivoCancelamento) : this()
    {
        UsuarioId = usuarioId;
        ReceitaManipulada = receitaManipulada;
        Status = status;
        DataHora = DateTime.UtcNow;
        MotivoCancelamento = motivoCancelamento;
    }

    public virtual ReceitaManipulada ReceitaManipulada { get; init; }
    public Guid ReceitaManipuladaId { get; private set; }
    public virtual Usuario Usuario { get; private set; }
    public Guid UsuarioId { get; private set; }
    public DateTime DataHora { get; private set; }
    public StatusReceita Status { get; private set; }
    public string MotivoCancelamento { get; private set; }
}