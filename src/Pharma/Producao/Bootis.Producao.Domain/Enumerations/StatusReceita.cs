using System.ComponentModel;

namespace Bootis.Producao.Domain.Enumerations;

public enum StatusReceita
{
    [Description("Aguardando Conferência")]
    AguardandoConferencia = 1,

    [Description("Em Conferência")]
    EmConferencia = 2,

    [Description("Em Conferência Farmacêutica")]
    EmConferenciaFarmaceutica = 3,

    [Description("Conferência Farmacêutica Finalizada")]
    ConferenciaFarmaceuticaFinalizada = 4,

    [Description("Em Pesagem")]
    EmPesagem = 5,

    [Description("Pesagem Finalizada")]
    PesagemFinalizada = 6,

    [Description("Em Peso Médio")]
    EmPesoMedio = 7,

    [Description("Peso Médio Finalizado")]
    PesoMedioFinalizado = 8,

    [Description("Em Conferência Final")]
    EmConferenciaFinal = 9,

    [Description("Conferência Final Finalizada")]
    ConferenciaFinalFinalizada = 10,

    [Description("Receita Finalizada")]
    ReceitaFinalizada = 11,

    [Description("Receita Cancelada")]
    ReceitaCancelada = 12,

    [Description("Receita Rejeitada")]
    ReceitaRejeitada = 13,

    // Legacy values - keeping for backward compatibility
    [Description("Conferência Farmacêutica")]
    ConferenciaFarmaceutica = 14,
    [Description("Peso Médio")] PesoMedio = 15,
    [Description("Pesagem")] Pesagem = 16,
    [Description("Emissão Rótulo")] EmissaoRotulo = 17,

    [Description("Emissão Ordem Manipulação")]
    EmissaoOrdemManipulacao = 18,
    [Description("Finalizado")] Finalizado = 19,
    [Description("Rejeitado")] Rejeitado = 20,
    [Description("Cancelado")] Cancelado = 21,
    [Description("Conferência Final")] ConferenciaFinal = 22
}