using Bootis.Producao.Domain.AggregatesModel.LaboratorioAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Infrastructure;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Bootis.Producao.Infrastructure.Repositories;

public class LaboratorioRepository(IUserContext userContext, IDbContext context)
    : Repository<Laboratorio>(context), ILaboratorioRepository
{
    public async Task<Laboratorio> ObterPorIdAsync(Guid id)
    {
        return await DbSet
            .Include(c => c.Empresa)
            .SingleOrDefaultAsync(c =>
                c.Id == id && c.GroupTenantId == userContext.GroupTenantId);
    }

    public async Task<List<Laboratorio>> ObterPorIdsAsync(IEnumerable<Guid> ids)
    {
        return await Context.Set<Laboratorio>()
            .Where(c => ids.Contains(c.Id))
            .Include(c => c.Empresa)
            .ToListAsync();
    }

    public async Task<bool> ValidarPorEmpresaIdEDescricaoAsync(Guid empresaId, string descricaoLaboratorio)
    {
        const string sql = """
                            SELECT CASE WHEN EXISTS (SELECT 1 
                                                    FROM laboratorios la
                                                    WHERE la.nome_laboratorio = @descricaoLaboratorio
                                                      AND la.empresa_id = @empresaId
                                                      AND la.group_tenant_id = @groupTenantId) 
                                       THEN 1 ELSE 0 END AS existe_registro
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new { descricaoLaboratorio, empresaId, userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public async Task<bool> ValidarPorIdEEmpresaIdEDescricaoAsync(Guid id, Guid empresaId,
        string descricaoLaboratorio)
    {
        const string sql = """
                            SELECT CASE WHEN EXISTS (SELECT 1 
                                                    FROM laboratorios la
                                                    WHERE la.id = @id
                                                      AND la.nome_laboratorio = @descricaoLaboratorio
                                                      AND la.empresa_id = @empresaId
                                                      AND la.group_tenant_id = @groupTenantId) 
                                       THEN 1 ELSE 0 END AS existe_registro
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new { id, descricaoLaboratorio, empresaId, userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public Task<int> VerificarDependeciaLaboratorioAsync(Guid id)
    {
        const string sql = """
                           SELECT COUNT(ff.laboratorio_id) AS total_dependencias
                           FROM laboratorios la
                           LEFT JOIN formas_farmaceutica ff ON
                                 ff.laboratorio_id = la.id
                           WHERE la.id = @id
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return dbConnection.QueryFirstOrDefaultAsync<int>(sql, new { id },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public async Task<List<Laboratorio>> ObterLaboratoriosSemModeloOrdemCadastradoAsync(
        string descricaoModelo, int tipoOrdemManipulacaoId)
    {
        const string sql = """
                           SELECT lab.*
                           FROM laboratorios lab
                           WHERE NOT EXISTS (
                               SELECT 1
                               FROM modelos_ordem_manipulacao mom
                               WHERE mom.laboratorio_id = lab.id
                                 AND mom.descricao = @DescricaoModelo
                                 AND mom.tipo_ordem_manipulacao = @tipoOrdemManipulacaoId
                                 AND mom.group_tenant_id = @GroupTenantId
                           )
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return (await dbConnection.QueryAsync<Laboratorio>(sql,
            new { descricaoModelo, tipoOrdemManipulacaoId, userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction())).ToList();
    }
}