using Bootis.Producao.Domain.AggregatesModel.ReceitaManipuladaAggregate;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Producao.Infrastructure.EntityConfigurations;

public class
    ReceitaManipuladaItemSinonimoEntityTypeConfiguration : IEntityTypeConfiguration<ReceitaManipuladaItemSinonimo>
{
    public void Configure(EntityTypeBuilder<ReceitaManipuladaItemSinonimo> builder)
    {
        builder.ToTable("receitas_manipuladas_item_sinonimo");

        builder.HasOne(x => x.ProdutoSinonimo)
            .WithMany()
            .HasForeignKey(x => x.ProdutoSinonimoId)
            .IsRequired(false);

        builder
            .Property(c => c.FatorEquivalencia)
            .Manipulacao();

        builder
            .Property(c => c.PercentualCorrecao)
            .Manipulacao();

        builder
            .Property(c => c.FatorCorrecao)
            .Manipulacao();
    }
}