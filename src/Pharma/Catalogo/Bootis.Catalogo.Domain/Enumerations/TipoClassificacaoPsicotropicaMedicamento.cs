using System.ComponentModel.DataAnnotations;

namespace Bootis.Catalogo.Domain.Enumerations;

public enum TipoClassificacaoPsicotropicaMedicamento
{
    [Display(Name = "Substâncias Entorpecentes")]
    A1 = 1,

    [Display(Name = "Substâncias Entorpecentes")]
    A2 = 2,

    [Display(Name = "Substâncias Psicotrópicas")]
    A3 = 3,

    [Display(Name = "Substâncias Psicotrópicas")]
    B1 = 4,

    [Display(Name = "Substâncias Psicotrópicas")]
    B2 = 5,

    [Display(Name = "Substâncias Imunossupressoras")]
    C3 = 6,

    [Display(Name = "Substâncias Sujeitas a Controle Especial")]
    C1 = 7,

    [Display(Name = "Substâncias Sujeitas a Controle Especial")]
    C2 = 8,

    [Display(Name = "Substâncias Sujeitas a Controle Especial")]
    C5 = 9
}