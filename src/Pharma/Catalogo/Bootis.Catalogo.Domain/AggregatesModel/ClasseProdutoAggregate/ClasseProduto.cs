using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.ClasseProdutoAggregate;

public class ClasseProduto : Enumeration
{
    public static readonly ClasseProduto MateriaPrima = new(1, nameof(TipoClasseProdutoAbreviacao.MateriaPrima));
    public static readonly ClasseProduto Embalagem = new(2, nameof(TipoClasseProdutoAbreviacao.Embalagem));
    public static readonly ClasseProduto ProdutoAcabado = new(3, nameof(TipoClasseProdutoAbreviacao.ProdutoAcabado));
    public static readonly ClasseProduto Servico = new(4, nameof(TipoClasseProdutoAbreviacao.Servico));
    public static readonly ClasseProduto CapsulaPronta = new(5, nameof(TipoClasseProdutoAbreviacao.CapsulaPronta));
    public static readonly ClasseProduto UsoConsumo = new(6, nameof(TipoClasseProdutoAbreviacao.UsoConsumo));
    public static readonly ClasseProduto TipoCapsula = new(7, nameof(TipoClasseProdutoAbreviacao.TipoCapsula));

    private ClasseProduto(int id, string name) : base(id, name)
    {
    }

    public UnidadeMedidaAbreviacao UnidadeCalculo { get; set; }
    public UnidadeMedidaAbreviacao UnidadePadraoVisualizacao { get; set; }
    public bool ControlaEstoque { get; set; }

    #region Navigation Properties

    public virtual ICollection<ClasseProdutoUnidadeVisualizacao> UnidadesVisualizacao { get; set; } =
        new List<ClasseProdutoUnidadeVisualizacao>();

    public virtual ICollection<ClasseProdutoUnidadePrescricao> UnidadesPrescricao { get; set; } =
        new List<ClasseProdutoUnidadePrescricao>();

    #endregion
}