using Bootis.Catalogo.Common;
using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.Dtos.ProdutoDiluido;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.ConversaoUnidadeMedida;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Common.UnidadeMedida.UnidadeMedidaAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.ProdutoDiluidoAggregate;

public class ProdutoDiluido : Entity, ITenant, IAggregateRoot
{
    public ProdutoDiluido()
    {
    }

    public ProdutoDiluido(Produto produto,
        FormaFarmaceutica formaFarmaceutica,
        IProdutoDiluidoDto produtoDiluidoDto) : this()
    {
        Produto = produto;
        FormaFarmaceutica = formaFarmaceutica;
        DosagemMinima = produtoDiluidoDto.DosagemMinima;
        DosagemMaxima = produtoDiluidoDto.DosagemMaxima;
        UnidadeMedidaId = produtoDiluidoDto.UnidadeMedidaId;
        Diluicao = produtoDiluidoDto.Diluicao;
        SeTodasFormasFarmaceuticas = produtoDiluidoDto.SeTodasFormasFarmaceuticas;
        SeQualquerDosagem = produtoDiluidoDto.SeQualquerDosagem;
    }

    public Guid ProdutoId { get; private set; }
    public Guid? FormaFarmaceuticaId { get; private set; }
    public decimal DosagemMinima { get; private set; }
    public decimal DosagemMaxima { get; private set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaId { get; private set; }
    public decimal Diluicao { get; private set; }
    public bool SeTodasFormasFarmaceuticas { get; private set; }
    public bool SeQualquerDosagem { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void Atualizar(FormaFarmaceutica formaFarmaceutica, IProdutoDiluidoDto produtoDiluidoDto)
    {
        FormaFarmaceutica = formaFarmaceutica;
        DosagemMinima = produtoDiluidoDto.DosagemMinima;
        DosagemMaxima = produtoDiluidoDto.DosagemMaxima;
        UnidadeMedidaId = produtoDiluidoDto.UnidadeMedidaId;
        Diluicao = produtoDiluidoDto.Diluicao;
        SeTodasFormasFarmaceuticas = produtoDiluidoDto.SeTodasFormasFarmaceuticas;
        SeQualquerDosagem = produtoDiluidoDto.SeQualquerDosagem;
    }

    public string ValidarProdutoDiluido(IProdutoDiluidoDto dto, FormaFarmaceutica formaFarmaceutica,
        TipoUnidade tipoUnidade)
    {
        if (SeQualquerDosagem && SeTodasFormasFarmaceuticas)
            throw new DomainException(nameof(CatalogoErrorCode),
                (int)CatalogoErrorCode.ProdutoDiluido_ValidationDosagem,
                Produto.Descricao);

        if (tipoUnidade != UnidadeMedidaCreator.Criar(UnidadeMedidaId).TipoUnidade && !SeQualquerDosagem) return null;

        if (formaFarmaceutica?.Id == FormaFarmaceutica?.Id)
        {
            if (SeQualquerDosagem)
                throw new DomainException(nameof(CatalogoErrorCode),
                    (int)CatalogoErrorCode.ProdutoDiluido_ValidationDosagem, Produto.Descricao);

            if (dto.SeQualquerDosagem)
                throw new DomainException(nameof(CatalogoErrorCode),
                    (int)CatalogoErrorCode.ProdutoDiluido_ValidationDosagem, Produto.Descricao);
        }
        else
        {
            if (!SeTodasFormasFarmaceuticas) return null;
        }


        var conversaoUnidadeMedida = ConversaoUnidadeMedidaCreator.Criar(UnidadeMedidaId, dto.UnidadeMedidaId);
        var dosagemMinimaConvertida = conversaoUnidadeMedida.CalcularConversao(DosagemMinima);
        var dosagemMaximaConvertida = conversaoUnidadeMedida.CalcularConversao(DosagemMaxima);

        return ValidarDosagens(dto.DosagemMinima, dto.DosagemMaxima, dosagemMinimaConvertida, dosagemMaximaConvertida,
            dto.UnidadeMedidaId);
    }

    private string ValidarDosagens(decimal dosagemMinima, decimal dosagemMaxima, decimal dosagemMinimaConvertida,
        decimal dosagemMaximaConvertida,
        UnidadeMedidaAbreviacao unidadeMedidaId)
    {
        if ((dosagemMinimaConvertida >= dosagemMinima && dosagemMinimaConvertida <= dosagemMaxima) ||
            (dosagemMaximaConvertida >= dosagemMinima && dosagemMaximaConvertida <= dosagemMaxima) ||
            (dosagemMinimaConvertida <= dosagemMinima && dosagemMaximaConvertida >= dosagemMaxima))
            return Localizer.Instance.GetMessage_ProdutoDiluido_DosagemInvalida(Id, dosagemMinimaConvertida,
                dosagemMaximaConvertida,
                dosagemMinima, dosagemMaxima, unidadeMedidaId.ToString());

        return null;
    }

    #region Navigation properties

    public virtual Produto Produto { get; set; }
    public virtual FormaFarmaceutica FormaFarmaceutica { get; set; }

    #endregion
}