using Bootis.Catalogo.Domain.AggregatesModel.CasAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.DcbAggregate;
using Bootis.Catalogo.Domain.Dtos.Produto.ProdutoMateriaPrima;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Shared.Common.UnidadeMedida.Enums;

namespace Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;

public class ProdutoMateriaPrima
{
    public ProdutoMateriaPrima()
    {
    }

    public ProdutoMateriaPrima(Produto produto,
        IProdutoMateriaPrimaDto materiaPrimaDto,
        Cas cas,
        Dcb dcb,
        ProdutoMateriaPrima produtoExcipienteEspecifico) : this()
    {
        Cas = cas;
        Dcb = dcb;
        UnidadePrescricaoId = materiaPrimaDto.UnidadePrescricaoId;
        TipoComponenteId = materiaPrimaDto.TipoComponenteId;
        Produto = produto;
        SomenteLaboratorio = materiaPrimaDto.SomenteLaboratorio;
        IsPellets = materiaPrimaDto.IsPellets;
        IsExcipiente = materiaPrimaDto.IsExcipiente;
        IsQsp = materiaPrimaDto.IsQsp;
        SomenteDiluido = materiaPrimaDto.SomenteDiluido;
        DiasValidade = materiaPrimaDto.DiasValidade;
        ExigeCapsulaGastroresistente = materiaPrimaDto.ExigeCapsulaGastroresistente;
        IsMonodroga = materiaPrimaDto.IsMonodroga;
        ToleranciaPesagemUp = materiaPrimaDto.ToleranciaPesagemUp;
        ToleranciaPesagemDown = materiaPrimaDto.ToleranciaPesagemDown;
        ObservacaoRotuloArmazenagem = materiaPrimaDto.ObservacaoRotuloArmazenagem;
        PesoMolecularSal = materiaPrimaDto.PesoMolecularSal.GetValueOrDefault();
        PesoMolecularBase = materiaPrimaDto.PesoMolecularBase.GetValueOrDefault();
        Valencia = materiaPrimaDto.Valencia;
        BiofarmaceuticaId = materiaPrimaDto.BiofarmaceuticaId;
        ProdutoExcipienteEspecifico = produtoExcipienteEspecifico;

        AtualizarInformacoesTecnicas(materiaPrimaDto.InformacaoTecnica);
        CalcularFatorEquivalencia();
    }

    public Guid ProdutoId { get; private set; }
    public Guid? DcbId { get; private set; }
    public Guid? CasId { get; private set; }
    public UnidadeMedidaAbreviacao? UnidadePrescricaoId { get; private set; }
    public TipoComponente? TipoComponenteId { get; private set; }
    public bool? SomenteLaboratorio { get; private set; }
    public bool? IsPellets { get; private set; }
    public bool? IsExcipiente { get; private set; }
    public bool? IsQsp { get; private set; }
    public bool? SomenteDiluido { get; private set; }
    public int? DiasValidade { get; private set; }
    public bool? ExigeCapsulaGastroresistente { get; private set; }
    public bool? IsMonodroga { get; private set; }
    public decimal? ToleranciaPesagemUp { get; private set; }
    public decimal? ToleranciaPesagemDown { get; private set; }
    public decimal PesoMolecularSal { get; private set; }
    public decimal PesoMolecularBase { get; private set; }
    public decimal FatorEquivalencia { get; private set; }
    public decimal? Valencia { get; private set; }
    public string ObservacaoRotuloArmazenagem { get; private set; }
    public TipoClasseBiofarmaceutica? BiofarmaceuticaId { get; private set; }
    public Guid? ProdutoExcipienteEspecificoId { get; private set; }
    public decimal? DiluicaoFornecedor { get; private set; }
    public decimal? FatorDiluicaoFornecedor { get; private set; }
    public decimal? ConcentracaoAgua { get; private set; }
    public decimal? FatorConcentracaoAgua { get; private set; }
    public decimal? DiluicaoInterna { get; private set; }
    public decimal? FatorDiluicaoInterna { get; private set; }
    public decimal? Densidade { get; private set; }
    public UnidadeMedidaAbreviacao? UnidadeAlternativaId1 { get; private set; }
    public UnidadeMedidaAbreviacao? UnidadeAlternativaConversaoId1 { get; private set; }
    public decimal? QuantidadeUnidadeAlternativa1 { get; private set; }
    public UnidadeMedidaAbreviacao? UnidadeAlternativaId2 { get; private set; }
    public UnidadeMedidaAbreviacao? UnidadeAlternativaConversaoId2 { get; private set; }
    public decimal? QuantidadeUnidadeAlternativa2 { get; private set; }

    public void Atualizar(IProdutoMateriaPrimaDto materiaPrimaDto,
        Cas cas, Dcb dcb, ProdutoMateriaPrima produtoExcipienteEspecifico)
    {
        ProdutoExcipienteEspecifico = produtoExcipienteEspecifico;
        UnidadePrescricaoId = materiaPrimaDto.UnidadePrescricaoId;
        TipoComponenteId = materiaPrimaDto.TipoComponenteId;
        Cas = cas;
        CasId = cas?.Id;
        Dcb = dcb;
        DcbId = dcb?.Id;
        SomenteLaboratorio = materiaPrimaDto.SomenteLaboratorio;
        IsPellets = materiaPrimaDto.IsPellets;
        IsExcipiente = materiaPrimaDto.IsExcipiente;
        IsQsp = materiaPrimaDto.IsQsp;
        SomenteDiluido = materiaPrimaDto.SomenteDiluido;
        DiasValidade = materiaPrimaDto.DiasValidade;
        ExigeCapsulaGastroresistente = materiaPrimaDto.ExigeCapsulaGastroresistente;
        IsMonodroga = materiaPrimaDto.IsMonodroga;
        ToleranciaPesagemUp = materiaPrimaDto.ToleranciaPesagemUp;
        ToleranciaPesagemDown = materiaPrimaDto.ToleranciaPesagemDown;
        ObservacaoRotuloArmazenagem = materiaPrimaDto.ObservacaoRotuloArmazenagem;
        PesoMolecularSal = materiaPrimaDto.PesoMolecularSal.GetValueOrDefault();
        PesoMolecularBase = materiaPrimaDto.PesoMolecularBase.GetValueOrDefault();
        Valencia = materiaPrimaDto.Valencia;

        AtualizarInformacoesTecnicas(materiaPrimaDto.InformacaoTecnica);
        CalcularFatorEquivalencia();
    }

    public void AtualizarInformacoesTecnicas(ProdutoMateriaPrimaInformacaoTecnicaDto informacaoTecnicaDto)
    {
        DiluicaoFornecedor = informacaoTecnicaDto?.DiluicaoFornecedor;
        FatorDiluicaoFornecedor = informacaoTecnicaDto?.FatorDiluicaoFornecedor;
        ConcentracaoAgua = informacaoTecnicaDto?.ConcentracaoAgua;
        FatorConcentracaoAgua = informacaoTecnicaDto?.FatorConcentracaoAgua;
        DiluicaoInterna = informacaoTecnicaDto?.DiluicaoInterna;
        FatorDiluicaoInterna = informacaoTecnicaDto?.FatorDiluicaoInterna;
        Densidade = informacaoTecnicaDto?.Densidade;
        UnidadeAlternativaId1 = informacaoTecnicaDto?.UnidadeAlternativaId1;
        UnidadeAlternativaConversaoId1 = informacaoTecnicaDto?.UnidadeAlternativaConversaoId1;
        QuantidadeUnidadeAlternativa1 = informacaoTecnicaDto?.QuantidadeUnidadeAlternativa1;
        UnidadeAlternativaId2 = informacaoTecnicaDto?.UnidadeAlternativaId2;
        UnidadeAlternativaConversaoId2 = informacaoTecnicaDto?.UnidadeAlternativaConversaoId2;
        QuantidadeUnidadeAlternativa2 = informacaoTecnicaDto?.QuantidadeUnidadeAlternativa2;
    }

    public void CalcularFatorEquivalencia()
    {
        if (PesoMolecularSal > 0 || PesoMolecularBase > 0) FatorEquivalencia = PesoMolecularSal / PesoMolecularBase;
    }

    public void AtualizarTipoComponenteId(TipoComponente tipoComponenteId)
    {
        TipoComponenteId = tipoComponenteId;
    }

    #region Navigation properties

    public virtual Produto Produto { get; set; }
    public virtual Dcb Dcb { get; set; }
    public virtual Cas Cas { get; set; }
    public virtual ProdutoMateriaPrima ProdutoExcipienteEspecifico { get; set; }

    #endregion
}