using Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoFormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Catalogo.Domain.AggregatesModel.EmbalagemClassificacaoAggregate;

public class EmbalagemClassificacao : Entity, IAggregateRoot, ITenant
{
    public EmbalagemClassificacao()
    {
    }

    public EmbalagemClassificacao(string descricao,
        bool ativo)
    {
        Descricao = descricao;
        Ativo = ativo;
    }

    public string Descricao { get; private set; }
    public bool Ativo { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void AtualizarDescricao(string descricao)
    {
        Descricao = descricao;
    }

    public void AtualizarStatus(bool ativo)
    {
        Ativo = ativo;
    }

    #region Navigation properties

    public virtual ICollection<ProdutoEmbalagem> ProdutoEmbalagem { get; set; } = new List<ProdutoEmbalagem>();

    public virtual ICollection<EmbalagemClassificacaoFormaFarmaceutica> EmbalagemClassificacaoFormaFarmaceutica
    {
        get;
        set;
    } = new List<EmbalagemClassificacaoFormaFarmaceutica>();

    #endregion
}