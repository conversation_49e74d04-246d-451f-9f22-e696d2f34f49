using Asp.Versioning;
using Bootis.Catalogo.Application.Requests.Ncm.Listar;
using Bootis.Catalogo.Application.Requests.Ncm.Obter;
using Bootis.Shared.Common.Query;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Catalogo.Api.Controllers.V1;

[Authorize]
[ApiController]
[ApiVersion(1)]
[ApiExplorerSettings(GroupName = "Catalogo")]
[Route("catalogo/v{version:apiVersion}/[controller]")]
public class NcmController(IMediator mediator) : ControllerBase
{
    [HttpGet]
    [Route("{id:Guid}")]
    [ProducesResponseType(typeof(ObterResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Obter(Guid id)
    {
        var result = await mediator.Send(new ObterRequest { Id = id });

        return Ok(result);
    }

    [HttpGet]
    [Route("ObterPorCodigo/{codigo}")]
    [ProducesResponseType(typeof(ObterPorCodigoResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ObterPorCodigo(string codigo)
    {
        var result = await mediator.Send(new ObterPorCodigoRequest { Codigo = codigo });

        return Ok(result);
    }

    [HttpGet]
    [Route("Listar")]
    [ProducesResponseType(typeof(PaginatedResult<ListarResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Listar([FromQuery] ListarRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }
}