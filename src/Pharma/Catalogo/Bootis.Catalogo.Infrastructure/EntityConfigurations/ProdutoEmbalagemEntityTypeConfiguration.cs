using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Catalogo.Infrastructure.EntityConfigurations;

public class ProdutoEmbalagemEntityTypeConfiguration : IEntityTypeConfiguration<ProdutoEmbalagem>
{
    public void Configure(EntityTypeBuilder<ProdutoEmbalagem> builder)
    {
        builder.ToTable("produtos_embalagem");

        builder.HasKey(e => e.ProdutoId);

        builder
            .HasOne(c => c.Produto)
            .WithOne(c => c.ProdutoEmbalagem)
            .HasForeignKey<ProdutoEmbalagem>(c => c.ProdutoId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .HasOne(c => c.EmbalagemClassificacao)
            .WithMany(c => c.ProdutoEmbalagem)
            .HasForeignKey(c => c.EmbalagemClassificacaoId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .Property(c => c.Volume)
            .EstoquePrecisao()
            .IsRequired(false);
    }
}