using Bootis.Catalogo.Domain.AggregatesModel.ClasseProdutoAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Catalogo.Infrastructure.EntityConfigurations;

public class ClasseProdutoEntityTypeConfiguration : BaseEnumerationEntityTypeConfiguration<ClasseProduto>
{
    protected override string TableName => "classes_produto";

    public override void Configure(EntityTypeBuilder<ClasseProduto> builder)
    {
        builder
            .Property(c => c.UnidadePadraoVisualizacao)
            .IsRequired();

        builder
            .Property(c => c.UnidadeCalculo)
            .IsRequired();

        builder
            .Property(c => c.ControlaEstoque)
            .IsRequired();

        builder
            .Property(c => c.Name)
            .HasColumnName("abreviacao")
            .Abreviacao(TamanhoTexto.Vinte)
            .IsRequired();

        base.Configure(builder);
    }
}