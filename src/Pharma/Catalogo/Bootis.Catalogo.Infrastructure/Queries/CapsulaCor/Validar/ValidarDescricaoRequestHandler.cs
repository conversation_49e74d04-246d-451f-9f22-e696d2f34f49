using System.Data;
using System.Text;
using Bootis.Catalogo.Application.Requests.CapsulaCor.Validar;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.CapsulaCor.Validar;

public class ValidarDescricaoRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ValidarDescricaoRequest>
{
    public async Task Handle(ValidarDescricaoRequest request, CancellationToken cancellationToken)
    {
        var sql = new StringBuilder("""
                                    SELECT true
                                    FROM capsulas_cor
                                    WHERE UPPER(cor_capsula) ILIKE UPPER(@descricao)
                                      AND group_tenant_id = @groupTenantId
                                    """);

        if (request.CapsulaCorId != null)
            sql.Append($" AND id != '{request.CapsulaCorId}' ");

        var result = await connection.QueryFirstOrDefaultAsync<bool>(sql.ToString(),
            new { descricao = request.Descricao, groupTenantId = userContext.GroupTenantId });

        if (result)
            throw new DomainException(Localizer.Instance.GetMessage_CapsulaCor_DescricaoExistente(request.Descricao));
    }
}