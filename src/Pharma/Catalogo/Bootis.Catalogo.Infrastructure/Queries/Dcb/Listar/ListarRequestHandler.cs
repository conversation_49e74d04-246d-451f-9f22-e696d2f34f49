using System.Data;
using Bootis.Catalogo.Application.Requests.Dcb.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.Dcb.Listar;

public class ListarRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarRequest, PaginatedResult<ListarResponse>>
{
    public Task<PaginatedResult<ListarResponse>> Handle(ListarRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT 
                                DCB.nome_dcb,
                                DCB.id,
                                DCB.numero_dcb
                           FROM dcbs DCB
                           WHERE 1 = 1 
                                 !@SEARCH_CONDITION@!
                           """;

        var searchNumeroDcb = new StringSearchField
        {
            Field = "DCB.numero_dcb",
            CompareType = StringCompareType.Contains
        };

        var searchNomeDcb = new StringSearchField
        {
            Field = "DCB.nome_dcb",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarRequest, ListarResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchNumeroDcb)
            .AddSearchField(searchNomeDcb)
            .ExecuteAsync();
    }
}