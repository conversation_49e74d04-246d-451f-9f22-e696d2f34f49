using System.Data;
using Bootis.Catalogo.Application.Requests.Grupo.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.Grupo.Listar;

public class ListarRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarRequest, PaginatedResult<ListarResponse>>
{
    public Task<PaginatedResult<ListarResponse>> Handle(ListarRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT 
                               gr.descricao,
                               gr.id
                           FROM produto_grupos gr
                           WHERE gr.group_tenant_id = @GroupTenantId
                                 !@SEARCH_CONDITION@!
                           """;

        var searchGrupo = new StringSearchField
        {
            Field = "GR.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarRequest, ListarResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchGrupo)
            .ExecuteAsync();
    }
}