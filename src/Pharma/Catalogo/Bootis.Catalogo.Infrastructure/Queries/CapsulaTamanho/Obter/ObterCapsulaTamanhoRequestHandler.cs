using System.Data;
using System.Text;
using Bootis.Catalogo.Application.Requests.CapsulaTamanho.Obter;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.CapsulaTamanho.Obter;

public class ObterCapsulaTamanhoRequestHandler(
    IDbConnection connection) : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        var sql = new StringBuilder("""
                                    SELECT CT.id,
                                           CT.numero_capsula,
                                           CT.volume_ml
                                    FROM capsulas_tamanho CT
                                    WHERE CT.Id = @Id
                                    """);

        var result = await connection.QueryFirstOrDefaultAsync<ObterResponse>(sql.ToString(), new { request.Id });

        if (result is null)
        {
            var message = Localizer.Instance.GetMessage_CapsulaTamanho_IdNaoEncontrado(request.Id);
            throw new DomainException(message);
        }

        return result;
    }
}