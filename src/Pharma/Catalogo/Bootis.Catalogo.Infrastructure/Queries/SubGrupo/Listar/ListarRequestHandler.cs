using System.Data;
using Bootis.Catalogo.Application.Requests.SubGrupo.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.FilterFields;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.SubGrupo.Listar;

public class ListarRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarRequest, PaginatedResult<ListarResponse>>
{
    public Task<PaginatedResult<ListarResponse>> Handle(ListarRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                               SELECT 
                                   sb.descricao,
                                   sb.id
                               FROM sub_grupos sb
                               LEFT JOIN produto_grupos gr ON gr.id = sb.grupo_id
                               WHERE sb.group_tenant_id = @GroupTenantId
                                     !@SEARCH_CONDITION@!
                           """;

        var searchSubGrupoDescricao = new StringSearchField
        {
            Field = "sb.descricao",
            CompareType = StringCompareType.Contains
        };

        var conditionalGrupoIds = new ConditionalFilterField<ListarRequest>
        {
            Filter = "gr.id = ANY ( @GruposId )",
            Predicate = filter => filter.GruposId != null
        };

        return PaginatedQueryBuilder<ListarRequest, ListarResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchSubGrupoDescricao)
            .AddFilter(conditionalGrupoIds)
            .ExecuteAsync();
    }
}