using System.Data;
using Bootis.Catalogo.Application.Requests.ProdutoSinonimo.Obter;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.ProdutoSinonimo.Obter;

public class ObterRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT ps.id,
                                  prod.id AS produto_id,
                                  ps.sinonimo,
                                  ps.descricao_rotulo,
                                  ps.fator_equivalencia,
                                  ps.percentual_correcao
                           FROM produtos_sinonimo ps
                                    LEFT JOIN produtos prod ON prod.id = ps.produto_id
                           WHERE ps.id = @id
                             AND ps.group_tenant_id = @groupTenantId
                           """;

        var result = await connection.QueryFirstOrDefaultAsync<ObterResponse>(sql,
                new { request.Id, userContext.GroupTenantId })
            .ConfigureAwait(false);

        if (result is not null) return result;
        var message = Localizer.Instance.GetMessage_ProdutoSinonimo_GuidNaoEncontrado(request.Id);
        throw new DomainException(message);
    }
}