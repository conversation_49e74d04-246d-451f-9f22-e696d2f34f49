using System.Data;
using Bootis.Catalogo.Application.Requests.Produto.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.FilterFields;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.Produto.Listar;

public class ListarRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarRequest, PaginatedResult<ListarResponse>>
{
    public Task<PaginatedResult<ListarResponse>> Handle(ListarRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT pro.descricao,
                                  pro.id,
                                  pro.sequencia_group_tenant,
                                  pro.classe_produto_id,
                                  pro.unidade_estoque_id AS unidade_medida_id,
                                  cl.abreviacao AS classe_produto_descricao
                           FROM produtos pro
                                LEFT JOIN classes_produto cl ON pro.classe_produto_id = cl.id
                           WHERE pro.group_tenant_id = @GroupTenantId
                                 !@SEARCH_CONDITION@!
                           """;

        var conditionalSomenteAtivos = new ConditionalFilterField<ListarRequest>
        {
            Filter = "PRO.ativo = true",
            Predicate = filter => filter.SomenteAtivos
        };

        var conditionalClassesProdutoIds = new ConditionalFilterField<ListarRequest>
        {
            Filter = "PRO.classe_produto_id = ANY ( @ClassesProdutoIds )",
            Predicate = filter => filter.ClassesProdutoIds != null
        };

        var searchCodigoProduto = new NumberSearchField
        {
            Field = "PRO.sequencia_group_tenant",
            CompareType = NumericCompareType.Contains
        };

        var searchProduto = new StringSearchField
        {
            Field = "PRO.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarRequest, ListarResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchCodigoProduto)
            .AddSearchField(searchProduto)
            .AddFilter(conditionalSomenteAtivos)
            .AddFilter(conditionalClassesProdutoIds)
            .ExecuteAsync();
    }
}