using System.Data;
using Bootis.Catalogo.Application.Requests.Produto.Obter;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Dapper;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.Produto.Obter;

public class ObterRequestHandler(
    IUserContext userContext,
    IDbConnection connection,
    IMediator mediator)
    : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        var sql = """
                  SELECT p.id,
                         p.sequencia_group_tenant codigo_produto,
                         p.descricao,
                         p.descricao_rotulo,
                         p.ativo,
                         p.classe_produto_id,
                         p.unidade_estoque_id,
                         g.descricao grupo,
                         g.id grupo_id,
                         sb.descricao subgrupo,
                         sb.id subgrupo_id,
                         p.unidade_estoque_id,
                         ef.nome fornecedor,
                         ef.id fornecedor_id,
                         p.controla_lote,
                         p.uso_continuo,
                         p.etiqueta,
                         p.controla_qualidade,
                         p.valor_custo,
                         p.margem_lucro,
                         p.valor_venda,
                         p.tipo_classificacao_psicotropica_medicamento_id,
                         p.tipo_tarja_medicamento_id,
                         p.desativar_projecao_estoque,
                         p.estoque_minimo,
                         p.estoque_maximo
                    FROM produtos p
                         LEFT JOIN fornecedores ef ON
                                   ef.id = p.fornecedor_id
                         LEFT JOIN sub_grupos sb ON
                                   p.sub_grupo_id = sb.id
                         LEFT JOIN produto_grupos g ON
                                   g.id = sb.grupo_id
                   WHERE p.id = @id 
                     AND p.group_tenant_id = @groupTenantId;

                  """;

        var produto = await connection.QuerySingleOrDefaultAsync<ObterResponse>(sql,
            new { request.Id, userContext.GroupTenantId });

        if (produto is null)
            throw new DomainException(Localizer.Instance.GetMessage_Produto_GuidNaoEncontrado(request.Id));

        switch (produto.ClasseProdutoId)
        {
            case TipoClasseProdutoAbreviacao.MateriaPrima:
                await HandleMateriaPrimaAsync(produto, request.Id, cancellationToken);
                break;

            case TipoClasseProdutoAbreviacao.Embalagem:
                await HandleEmbalagemAsync(produto, request.Id, cancellationToken);
                break;

            case TipoClasseProdutoAbreviacao.TipoCapsula:
                await HandleTipoCapsulaAsync(produto, request.Id, cancellationToken);
                break;

            case TipoClasseProdutoAbreviacao.CapsulaPronta:
                await HandleCapsulaProntaAsync(produto, request.Id, cancellationToken);
                break;
        }

        return produto;
    }

    private async Task HandleMateriaPrimaAsync(ObterResponse produto, Guid id,
        CancellationToken cancellationToken)
    {
        ObterMateriaPrimaRequest materiaPrimaRequest = new()
        {
            Id = id
        };

        var materiaPrimaResponse = await mediator.Send(materiaPrimaRequest, cancellationToken);

        if (materiaPrimaResponse != null) produto.MateriaPrima = materiaPrimaResponse;
    }

    private async Task HandleEmbalagemAsync(ObterResponse produto, Guid id, CancellationToken cancellationToken)
    {
        ObterEmbalagemRequest embalagemRequest = new()
        {
            Id = id
        };

        var embalagemResponse = await mediator.Send(embalagemRequest, cancellationToken);

        if (embalagemResponse != null)
        {
            produto.Embalagem = embalagemResponse;

            if (embalagemResponse.EmbalagemAssociacoes.Any(c => c.ProdutoId != Guid.Empty))
                produto.Embalagem.EmbalagemAssociacoes = embalagemResponse.EmbalagemAssociacoes;

            if (embalagemResponse.NumeroCapsulaAssociacoes.Any(c => c.NumeroCapsulaId != Guid.Empty))
                produto.Embalagem.NumeroCapsulaAssociacoes = embalagemResponse.NumeroCapsulaAssociacoes;
        }
    }

    private async Task HandleTipoCapsulaAsync(ObterResponse produto, Guid id,
        CancellationToken cancellationToken)
    {
        ObterCapsulaRequest capsulaRequest = new()
        {
            Id = id
        };

        var capsulaResponse = await mediator.Send(capsulaRequest, cancellationToken);

        if (capsulaResponse != null) produto.TipoCapsula = capsulaResponse;
    }

    private async Task HandleCapsulaProntaAsync(ObterResponse produto, Guid id,
        CancellationToken cancellationToken)
    {
        ObterCapsulaProntaRequest capsulaProntaRequest = new()
        {
            Id = id
        };

        var capsulaProntaResponse = await mediator.Send(capsulaProntaRequest, cancellationToken);

        if (capsulaProntaResponse != null)
        {
            produto.CapsulaPronta = capsulaProntaResponse;

            if (capsulaProntaResponse.MateriaPrimaAssociacoes.Any(c => c.ProdutoId != Guid.Empty))
                produto.CapsulaPronta.MateriaPrimaAssociacoes = capsulaProntaResponse.MateriaPrimaAssociacoes;
        }
    }
}