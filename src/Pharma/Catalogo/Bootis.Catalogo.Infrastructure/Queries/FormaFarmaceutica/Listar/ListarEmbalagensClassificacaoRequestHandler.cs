using System.Data;
using Bootis.Catalogo.Application.Requests.FormaFarmaceutica.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.FormaFarmaceutica.Listar;

public class ListarEmbalagensClassificacaoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarEmbalagensClassificacaoRequest,
        PaginatedResult<ListarEmbalagensClassificacaoResponse>>
{
    public Task<PaginatedResult<ListarEmbalagensClassificacaoResponse>> Handle(
        ListarEmbalagensClassificacaoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT ec.descricao AS embalagem_classificacao_descricao,
                                  ec.id AS embalagem_classificacao_id,
                                  ec.ativo,
                                  CASE 
                                      WHEN EXISTS (
                                          SELECT 1
                                          FROM formas_farmaceutica ff
                                          JOIN embalagens_classificacao_forma_farmaceutica ecff 
                                              ON ecff.forma_farmaceutica_id = ff.id
                                          WHERE ff.id = @Id
                                            AND ec.id = ecff.embalagem_classificacao_id
                                      ) 
                                      THEN 1 ELSE 0 
                                  END AS se_vinculo_forma_farmaceutica
                           FROM embalagens_classificacao ec
                           WHERE ec.group_tenant_id = @GroupTenantId 
                                 !@SEARCH_CONDITION@!
                           """;

        var searchDescricao = new StringSearchField
        {
            Field = "EC.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarEmbalagensClassificacaoRequest, ListarEmbalagensClassificacaoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchDescricao)
            .ExecuteAsync();
    }
}