using System.Data;
using Bootis.Catalogo.Application.Requests.ProdutoDiluido.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.ProdutoDiluido.Listar;

public class
    ListarDetalhadoRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarDetalhadoRequest, PaginatedResult<ListarDetalhadoResponse>>
{
    public Task<PaginatedResult<ListarDetalhadoResponse>> Handle(ListarDetalhadoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT ff.descricao AS forma_farmaceutica_descricao,
                                  pd.id,
                                  ff.id AS forma_farmaceutica_id,
                                  pd.dosagem_minima,
                                  pd.dosagem_maxima,
                                  pd.unidade_medida_id,
                                  un.abreviacao AS unidade_medida_abreviacao,
                                  pd.se_todas_formas_farmaceuticas
                           FROM produtos_diluido pd
                                    LEFT JOIN produtos prod ON prod.id = pd.produto_id
                                    LEFT JOIN formas_farmaceutica ff ON ff.id = pd.forma_farmaceutica_id
                                    LEFT JOIN unidades_medida un ON un.id = pd.unidade_medida_id
                           WHERE prod.id = @ProdutoId
                             AND pd.group_tenant_id = @GroupTenantId
                                 !@SEARCH_CONDITION@!
                           """;

        var searchFormaFarmaceutica = new StringSearchField
        {
            Field = "FF.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarDetalhadoRequest, ListarDetalhadoResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchFormaFarmaceutica)
            .ExecuteAsync();
    }
}