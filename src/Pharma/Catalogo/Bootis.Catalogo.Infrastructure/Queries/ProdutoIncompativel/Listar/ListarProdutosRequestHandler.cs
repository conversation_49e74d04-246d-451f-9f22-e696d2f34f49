using System.Data;
using Bootis.Catalogo.Application.Requests.ProdutoIncompativel.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.ProdutoIncompativel.Listar;

public class
    ListarProdutosRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarProdutosRequest, PaginatedResult<ListarProdutosResponse>>
{
    public Task<PaginatedResult<ListarProdutosResponse>> Handle(ListarProdutosRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT pro.descricao,
                                  pro.id,
                                  CASE WHEN EXISTS (SELECT 1
                                                    FROM produtos p
                                                             JOIN produtos_incompativel prodi ON prodi.produto_id = p.id
                                                    WHERE p.id = @ProdutoId
                                                      AND pro.id = prodi.produto_incompativel_id) THEN 1 ELSE 0 END AS se_vinculo_produto
                           FROM produtos pro
                           WHERE pro.group_tenant_id = @GroupTenantId
                             AND pro.classe_produto_id = 1
                             !@SEARCH_CONDITION@!
                           """;

        var searchProduto = new StringSearchField
        {
            Field = "PRO.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarProdutosRequest, ListarProdutosResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchProduto)
            .ExecuteAsync();
    }
}