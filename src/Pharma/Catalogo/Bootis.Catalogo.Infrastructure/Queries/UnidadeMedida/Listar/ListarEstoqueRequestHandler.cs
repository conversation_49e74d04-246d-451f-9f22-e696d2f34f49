using System.Data;
using Bootis.Catalogo.Application.Requests.UnidadeMedida.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.UnidadeMedida.Listar;

public class ListarEstoqueRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarEstoqueRequest, PaginatedResult<ListarEstoqueResponse>>
{
    public Task<PaginatedResult<ListarEstoqueResponse>> Handle(ListarEstoqueRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT un.id,
                                  un.abreviacao AS unidade_abreviacao,
                                  un.descricao,
                                  un.ativo,
                                  un.unidade_alternativa
                           FROM unidades_medida un
                           WHERE un.unidade_estoque = true
                           """;

        var searchAbreviacao = new StringSearchField
        {
            Field = "UN.abreviacao",
            CompareType = StringCompareType.Contains
        };

        var searchDescricao = new StringSearchField
        {
            Field = "UN.descricao",
            CompareType = StringCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarEstoqueRequest, ListarEstoqueResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchAbreviacao)
            .AddSearchField(searchDescricao)
            .ExecuteAsync();
    }
}