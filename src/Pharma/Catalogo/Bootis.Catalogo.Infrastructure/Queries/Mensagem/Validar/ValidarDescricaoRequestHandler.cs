using System.Data;
using System.Text;
using Bootis.Catalogo.Application.Requests.Mensagem.Validar;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Catalogo.Infrastructure.Queries.Mensagem.Validar;

public class ValidarDescricaoRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ValidarDescricaoRequest>
{
    public async Task Handle(ValidarDescricaoRequest request, CancellationToken cancellationToken)
    {
        var sql = new StringBuilder("""
                                    SELECT true
                                    FROM mensagens
                                    WHERE UPPER(descricao) ILIKE UPPER(@descricao)
                                      AND group_tenant_id = @groupTenantId
                                    """);

        if (request.MensagemId != null)
            sql.Append($" id != '{request.MensagemId}' ");

        var result = await connection
            .QueryFirstOrDefaultAsync<bool>(sql.ToString(),
                new { request.Descricao, userContext.GroupTenantId })
            .ConfigureAwait(false);

        if (result)
            throw new DomainException(Localizer.Instance.GetMessage_MensagemDescricao_JaExiste());
    }
}