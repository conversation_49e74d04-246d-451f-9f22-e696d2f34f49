using System.Text;
using Bootis.Catalogo.Domain.AggregatesModel.GrupoAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Infrastructure;
using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;

namespace Bootis.Catalogo.Infrastructure.Repositories;

public class GrupoProdutoRepository(IUserContext userContext, IDbContext context)
    : Repository<Grupo>(context), IGrupoRepository
{
    public Task<List<Grupo>> ObterPorIdsAsync(IEnumerable<Guid> ids)
    {
        return Context.Set<Grupo>()
            .Where(c => ids.Contains(c.Id))
            .ToListAsync();
    }

    public async Task<bool> ValidarPorDescricaoAsync(string descricao)
    {
        const string sql = """
                            SELECT CASE WHEN EXISTS (SELECT 1 
                                                       FROM produto_grupos g
                                                      WHERE g.descricao = @descricao
                                                        AND g.group_tenant_id = @groupTenantId) THEN 1 ELSE 0 END AS existe_registro;
                           """;

        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new { descricao, groupTenantId = userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public async Task<bool> ValidarPorIdEhDescricaoAsync(Guid id, string descricao)
    {
        const string sql = """
                            SELECT CASE WHEN EXISTS (SELECT 1 
                                                       FROM produto_grupos g
                                                      WHERE g.id = @id
                                                        AND g.descricao = @descricao
                                                        AND g.group_tenant_id = @groupTenantId) THEN 1 ELSE 0 END AS existe_registro;
                           """;
        var dbConnection = Context.Database.GetDbConnection();

        return await dbConnection.QuerySingleOrDefaultAsync<bool>(sql,
            new { id, descricao, groupTenantId = userContext.GroupTenantId },
            Context.Database.CurrentTransaction?.GetDbTransaction());
    }

    public Task<Grupo> ObterPorIdAsync(Guid id)
    {
        return DbSet
            .Include(c => c.SubGrupos)
            .ThenInclude(p => p.Produtos)
            .FirstOrDefaultAsync(c =>
                c.Id == id && c.GroupTenantId == userContext.GroupTenantId);
    }

    public async Task<IEnumerable<(Guid GrupoId, string GrupoDescricao, int TotalSubGrupos, int TotalProdutos)>>
        VerificarDependeciasGruposAsync(IEnumerable<Guid> ids)
    {
        var idsConverted = string.Join(", ", ids.Select(g => $"'{g}'"));

        var sql = new StringBuilder($"""
                                     SELECT gr.id,
                                            gr.descricao,
                                            COUNT(DISTINCT sgr.id) AS total_sub_grupos,
                                            COUNT(pr.id) AS total_produtos
                                       FROM produto_grupos gr
                                            LEFT JOIN sub_grupos sgr ON
                                                   sgr.grupo_id = gr.id
                                            LEFT JOIN produtos pr ON
                                                   pr.sub_grupo_id = sgr.id
                                      WHERE gr.id IN ({idsConverted})
                                     GROUP BY gr.descricao,
                                              gr.id
                                     HAVING COUNT(pr.sub_grupo_id) > 0
                                     """);

        var dbConnection = Context.Database.GetDbConnection();

        var queryResult = await dbConnection
            .QueryAsync<(Guid GrupoId, string GrupoDescricao, int TotalSubGrupos, int TotalProdutos)>(
                sql.ToString(), new { idsConverted }, Context.Database.CurrentTransaction?.GetDbTransaction());

        return queryResult;
    }
}