using Bootis.Shared.Common.Extensions;
using Bootis.Shared.Common.UnidadeMedida.Enums;
using Bootis.Shared.Common.UnidadeMedida.UnidadeMedidaAggregate;
using Bootis.Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace Bootis.Estoque.Infrastructure.Seeds;

public class UnidadeMedidaSeed : ISeed
{
    public int Order => 3;

    public void Seed(DbContext dbContext)
    {
        foreach (UnidadeMedidaAbreviacao tipo in Enum.GetValues(typeof(UnidadeMedidaAbreviacao)))
        {
            var unidadeMedida = UnidadeMedidaCreator.Criar(tipo);

            var id = tipo.ToInt();

            var linha =
                $"""
                 INSERT INTO unidades_medida (id, descricao, ativo, unidade_alternativa, unidade_estoque, unidade_prescricao, unidade_posologia, tipo_unidade, abreviacao) 
                 VALUES ('{id}', N'{unidadeMedida.Descricao}', {unidadeMedida.Ativo}, {unidadeMedida.UnidadeAlternativa}, {unidadeMedida.UnidadeEstoque}, {unidadeMedida.UnidadePrescricao}, {unidadeMedida.UnidadePosologia}, {(int)unidadeMedida.TipoUnidade}, N'{unidadeMedida.Abreviacao.ToString()}');
                 """;

            dbContext.Database.ExecuteSqlRaw(linha);
        }
    }
}