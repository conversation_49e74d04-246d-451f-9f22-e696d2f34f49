using Bootis.Catalogo.Application.Requests.Mensagem.Remover;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Catalogo.Application.Validations.MensagemValidation;

public class RemoverProdutosVinculadosRequestValidator : AbstractValidator<RemoverProdutosVinculadosRequest>
{
    public RemoverProdutosVinculadosRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(f => f.MensagemId)
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.MensagemId)));

        RuleFor(f => f.ProdutosIds)
            .NotEmpty()
            .WithMessage(c => localizer.GetMessage_Validation_CampoRequerido(nameof(c.ProdutosIds)));
    }
}