using Bootis.Catalogo.Application.Requests.ProdutoAssociado.Atualizar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Catalogo.Application.Validations.ProdutoAssociadoValidation;

public class AtualizarRequestValidator : AbstractValidator<AtualizarRequest>
{
    public AtualizarRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.Id)
            .NotEmpty()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.Id)));

        RuleFor(c => c.ProdutoAssociadoId)
            .NotEmpty()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.ProdutoAssociadoId)));

        RuleFor(c => c.FormaFarmaceuticaId)
            .NotEmpty()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.FormaFarmaceuticaId)));

        RuleFor(c => c.UnidadeMedidaDosagem)
            .IsInEnum();

        RuleFor(c => c.QuantidadeAssociada)
            .NotEmpty()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.QuantidadeAssociada)));

        RuleFor(c => c.UnidadeMedidaQuantidadeAssociada)
            .IsInEnum();

        RuleFor(c => c.TipoRelacaoQuantidade)
            .IsInEnum();
    }
}