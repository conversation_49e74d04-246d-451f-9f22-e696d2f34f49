using Bootis.Catalogo.Application.Requests.EmbalagemClassificacao.Cadastrar;
using FluentValidation;

namespace Bootis.Catalogo.Application.Validations.EmbalagemClassificacaoValidation;

public class CadastrarRequestValidator : AbstractValidator<CadastrarRequest>
{
    public CadastrarRequestValidator()
    {
        RuleFor(c => c.<PERSON>)
            .MaximumLength(80)
            .NotEmpty();
    }
}