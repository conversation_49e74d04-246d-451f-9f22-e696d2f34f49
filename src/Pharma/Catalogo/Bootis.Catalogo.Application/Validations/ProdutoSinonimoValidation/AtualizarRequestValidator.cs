using Bootis.Catalogo.Application.Requests.ProdutoSinonimo.Atualizar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Catalogo.Application.Validations.ProdutoSinonimoValidation;

public class AtualizarRequestValidator : AbstractValidator<AtualizarRequest>
{
    public AtualizarRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.Id)
            .NotEmpty()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.Id)));


        RuleFor(c => c.Sinonimo)
            .NotEmpty()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.Sinonimo)));
    }
}