using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Application.Requests.ProdutoAssociado.Atualizar;
using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAggregate;
using Bootis.Catalogo.Domain.AggregatesModel.ProdutoAssociadoAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Catalogo.Application.UseCases.ProdutoAssociado;

public class AtualizarRequestHandler(
    IUnitOfWork unitOfWork)
    : IRequestHandler<AtualizarRequest>
{
    public async Task Handle(AtualizarRequest request, CancellationToken cancellationToken)
    {
        var produtoAssociadoRepository =
            unitOfWork.GetRepository<IProdutoAssociadoRepository>();

        var produtoRepository = unitOfWork.GetRepository<IProdutoRepository>();
        var formaFarmaceuticaRepository =
            unitOfWork.GetRepository<IFormaFarmaceuticaRepository>();

        var produtoAssociado = await produtoAssociadoRepository.ObterProdutoAssociadoAsync(request.Id);
        var produtoAssociacao = await produtoRepository.ObterProdutoAsync(request.ProdutoAssociadoId);
        var formaFarmaceutica =
            await formaFarmaceuticaRepository.ObterFormaFarmaceuticaAsync(request.FormaFarmaceuticaId);

        await produtoAssociadoRepository.ValidarProdutoAssociadoPorIdEFormaFarmaceuticaAsync(
            request.Id, produtoAssociado.Produto, produtoAssociacao, formaFarmaceutica);

        ProdutoAssociadoRepositoryExtension.ValidarClassesProdutos(produtoAssociado.Produto, produtoAssociacao);

        produtoAssociado.Atualizar(produtoAssociacao, formaFarmaceutica, request.DosagemMinima,
            request.DosagemMaxima, request.UnidadeMedidaDosagem, request.QuantidadeAssociada,
            request.UnidadeMedidaQuantidadeAssociada, request.TipoRelacaoQuantidade, request.Acumula);

        produtoAssociadoRepository.Update(produtoAssociado);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}