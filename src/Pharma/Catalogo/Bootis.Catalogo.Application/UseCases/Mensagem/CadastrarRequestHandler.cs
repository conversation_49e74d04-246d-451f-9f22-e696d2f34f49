using Bootis.Catalogo.Application.Extensions;
using Bootis.Catalogo.Application.Requests.Mensagem.Cadastrar;
using Bootis.Catalogo.Domain.AggregatesModel.MensagemAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Catalogo.Application.UseCases.Mensagem;

public class CadastrarRequestHandler(
    IUnitOfWork unitOfWork) : IRequestHandler<CadastrarRequest>
{
    public async Task Handle(CadastrarRequest request, CancellationToken cancellationToken)
    {
        var mensagemRepository =
            unitOfWork.GetRepository<IMensagemRepository>();

        await mensagemRepository.ValidarMensagemPorDescricaoAsync(request.Descricao);

        var mensagem = new Domain.AggregatesModel.MensagemAggregate.Mensagem(request.Descricao,
            request.ExibeVenda,
            request.ExibeRotulagem,
            request.ExibeFichaPesagem,
            request.ExibeImpressaoRotulo);

        mensagemRepository.Add(mensagem);

        await unitOfWork.SaveChangesAsync(cancellationToken).ConfigureAwait(false);
    }
}