using Bootis.Catalogo.Application.Requests.Produto.Cadastrar;
using Bootis.Shared.Application.Interfaces;

namespace Bootis.Catalogo.Application.UseCases.Produto;

public class CadastrarBaseRequestHandler(
    IUnitOfWork unitOfWork)
    : CadastrarRequestHandler<CadastrarBaseRequest>(unitOfWork)
{
    protected override Task InternalCadastrarAsync(CadastrarBaseRequest request,
        Domain.AggregatesModel.ProdutoAggregate.Produto produto, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}