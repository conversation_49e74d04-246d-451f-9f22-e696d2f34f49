using Bootis.Catalogo.Application.Requests.SubGrupo.Remover;
using Bootis.Catalogo.Common;
using Bootis.Catalogo.Domain.AggregatesModel.SubGrupoAggregate;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Catalogo.Application.Extensions;

public static class SubGrupoRepositoryExtension
{
    public static async Task<SubGrupo> ObterSubGrupoAsync(this ISubGrupoRepository repository, Guid id)
    {
        var subGrupo = await repository.ObterPorIdAsync(id).ConfigureAwait(false);

        if (subGrupo == null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_SubGrupo_GuidNaoEncontrado(id));

        return subGrupo;
    }

    public static async Task<List<SubGrupo>> ObterSubGruposAsync(this ISubGrupoRepository repository,
        IEnumerable<Guid> ids)
    {
        var subGrupos = await repository.ObterPorIdsAsync(ids);

        var idsInvalidos = ids
            .Distinct()
            .Where(id => !subGrupos.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_SubGrupo_GuidNaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(nameof(idsInvalidos),
                idsInvalidos);

        return subGrupos;
    }

    public static async Task ValidarSubGrupoPorDescricaoAsync(this ISubGrupoRepository repository, Guid grupoId,
        string descricao)
    {
        if (await repository.ValidarPorDescricaoAsync(grupoId, descricao))
            throw new ValidationException(nameof(descricao),
                Localizer.Instance.GetMessage_SubGrupo_DescricaoExistente(descricao));
    }

    public static async Task ValidarSubGrupoPorIdEDescricaoAsync(this ISubGrupoRepository repository,
        Guid grupoId, Guid id, string descricao)
    {
        if (!await repository.ValidarPorIdEDescricaoAsync(grupoId, id, descricao))
            await ValidarSubGrupoPorDescricaoAsync(repository, grupoId, descricao);
    }

    public static async Task VerificarDependenciasAsync(this ISubGrupoRepository repository,
        ICollection<SubGrupo> subGrupos)
    {
        var removeResponses = new List<RemoverResponse>();
        var ids = subGrupos.Select(g => g.Id).ToList();
        var subGruposDependentes = await repository.VerificarDependeciasSubGruposAsync(ids);

        removeResponses.AddRange(subGruposDependentes.Select(x => new RemoverResponse
        {
            SubGrupoId = x.SubGrupoId,
            SubGrupoDescricao = x.SubGrupoDescricao,
            ProdutoCount = x.ProdutoCount
        }));

        if (removeResponses.Count > 0)
            throw new DomainException(nameof(CatalogoErrorCode),
                (int)CatalogoErrorCode.SubGrupo_Remover, removeResponses.ToArray());
    }
}