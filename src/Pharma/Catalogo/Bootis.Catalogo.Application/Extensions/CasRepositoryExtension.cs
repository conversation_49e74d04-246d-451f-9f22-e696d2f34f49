using Bootis.Catalogo.Domain.AggregatesModel.CasAggregate;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Catalogo.Application.Extensions;

public static class CasRepositoryExtension
{
    public static async Task<Cas?> ObterCasAsync(this ICasRepository repository, Guid? id)
    {
        if (!id.HasValue) return null;
        var cas = await repository.GetByIdAsync(id.Value);
        if (cas == null)
            throw new ValidationException(nameof(id), $"CAS com id {id} não encontrado.");
        return cas;
    }
}