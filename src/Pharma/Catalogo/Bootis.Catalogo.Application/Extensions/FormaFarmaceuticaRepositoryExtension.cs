using Bootis.Catalogo.Application.Requests.FormaFarmaceutica.Remover;
using Bootis.Catalogo.Domain.AggregatesModel.FormaFarmaceuticaAggregate;
using Bootis.Catalogo.Domain.Enumerations;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Catalogo.Application.Extensions;

public static class FormaFarmaceuticaRepositoryExtension
{
    public static async Task<FormaFarmaceutica> ObterFormaFarmaceuticaAsync(
        this IFormaFarmaceuticaRepository repository, Guid id)
    {
        var formaFarmaceutica = await repository.ObterPorIdAsync(id);

        if (formaFarmaceutica is null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_FormaFarmaceutica_GuidNaoEncontrado(id));

        return formaFarmaceutica;
    }

    public static async Task<FormaFarmaceutica> ObterFormaFarmaceuticaAsync(
        this IFormaFarmaceuticaRepository repository, Guid? id)
    {
        return id.HasValue ? await ObterFormaFarmaceuticaAsync(repository, id.Value) : null;
    }

    public static async Task<List<FormaFarmaceutica>> ObterFormasFarmaceuticasAsync(
        this IFormaFarmaceuticaRepository repository, IEnumerable<Guid> ids)
    {
        var formasFarmaceuticas = await repository.ObterPorIdsAsync(ids);

        var idsInvalidos = ids
            .Distinct()
            .Where(id => !formasFarmaceuticas.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_FormaFarmaceutica_GuidNaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(nameof(idsInvalidos),
                idsInvalidos);

        return formasFarmaceuticas;
    }

    public static async Task ValidarFormaFarmaceuticaPorDescricaoAsync(this IFormaFarmaceuticaRepository repository,
        string descricao)
    {
        var hasDescricao = await repository.ValidarPorDescricaoAsync(descricao);

        if (hasDescricao)
            throw new ValidationException(nameof(descricao),
                Localizer.Instance.GetMessage_FormaFarmaceutica_DescricaoExistente(descricao));
    }

    public static async Task ValidarFormaFarmaceuticaPorIdEDescricaoAsync(
        this IFormaFarmaceuticaRepository repository, Guid id, string descricao)
    {
        var hasDescricao = await repository.ValidarPorIdEDescricaoAsync(id, descricao);

        if (!hasDescricao) await ValidarFormaFarmaceuticaPorDescricaoAsync(repository, descricao);
    }

    public static async Task<TipoCalculo> ObterTipoCalculoAsync(this IFormaFarmaceuticaRepository repository,
        Guid formaFarmaceuticaId)
    {
        var tipoCalculo = await repository.ObterTipoCalculoPorIdAsync(formaFarmaceuticaId);

        if (!Enum.IsDefined(typeof(TipoCalculo), tipoCalculo))
            throw new ValidationException(nameof(formaFarmaceuticaId),
                Localizer.Instance.GetMessage_FormaFarmaceutica_GuidNaoEncontrado(formaFarmaceuticaId));

        return tipoCalculo;
    }

    public static async Task<List<FormaFarmaceutica>> ObterFormasFarmaceuticasSemModeloOrdemAsync(
        this IFormaFarmaceuticaRepository repository, string descricao, int tipoOrdemManipulacaoId)
    {
        return await repository.ObterFormasFarmaceuticasSemModeloOrdemCadastradoAsync(descricao,
            tipoOrdemManipulacaoId);
    }

    public static async Task VerificarDependenciasAsync(this IFormaFarmaceuticaRepository repository,
        IEnumerable<FormaFarmaceutica> formasFarmaceuticas)
    {
        var removeResponses = new List<RemoverResponse>();
        var ids = formasFarmaceuticas.Select(c => c.Id).ToList();
        var formasFarmaceuticasDependentes = await repository.VerificarDependenciaAsync(ids);

        removeResponses.AddRange(formasFarmaceuticasDependentes.Select(e => new RemoverResponse
        {
            Id = e.Id,
            Descricao = e.Descricao
        }));

        if (removeResponses.Count > 0)
            throw new DomainException(nameof(GlobalErrorCode), (int)GlobalErrorCode.Global_Remover,
                removeResponses.ToArray());
    }
}