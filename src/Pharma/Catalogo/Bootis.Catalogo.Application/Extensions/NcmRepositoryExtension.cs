using Bootis.Catalogo.Domain.AggregatesModel.NcmAggregate;
using Bootis.Catalogo.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Catalogo.Application.Extensions;

public static class NcmRepositoryExtension
{
    public static async Task<List<Ncm>> ObterNcmsAsync(this INcmRepository repository,
        IEnumerable<Guid> ncmIds)
    {
        var ncms = await repository.ObterNcmsPorIdsAsync(ncmIds);

        var idsInvalidos = ncmIds
            .Distinct()
            .Where(id => !ncms.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_Ncm_IdNaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(nameof(ncmIds),
                idsInvalidos);

        return ncms;
    }
}