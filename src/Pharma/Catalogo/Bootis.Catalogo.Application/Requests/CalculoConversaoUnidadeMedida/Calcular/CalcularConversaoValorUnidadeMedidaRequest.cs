using Bootis.Shared.Common.UnidadeMedida.Enums;
using MediatR;

namespace Bootis.Catalogo.Application.Requests.CalculoConversaoUnidadeMedida.Calcular;

public class CalcularConversaoValorUnidadeMedidaRequest(ICollection<ConversaoValorUnidadeMedidaACalcular> conversoes)
    : IRequest<CalcularConversaoValorUnidadeMedidaResponse>
{
    public ICollection<ConversaoValorUnidadeMedidaACalcular> Conversoes { get; set; } = conversoes;
}

public class ConversaoValorUnidadeMedidaACalcular
{
    public UnidadeMedidaAbreviacao UnidadeMedidaOrigemId { get; set; }
    public UnidadeMedidaAbreviacao UnidadeMedidaConversaoId { get; set; }
    public decimal Quantidade { get; set; }
}