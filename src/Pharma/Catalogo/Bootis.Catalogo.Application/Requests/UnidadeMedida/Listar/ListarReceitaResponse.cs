using System.Runtime.Serialization;

namespace Bootis.Catalogo.Application.Requests.UnidadeMedida.Listar;

public class ListarReceitaResponse
{
    public int UnidadeMedidaId { get; set; }
    public string Abreviacao { get; set; }
    public string Descricao { get; set; }
    public bool UnidadeAlternativa { get; set; }

    [IgnoreDataMember] public int UnidadePrescricaoId { get; set; }

    public bool UnidadePadraoProduto => UnidadePrescricaoId == UnidadeMedidaId && !UnidadeAlternativa;
}