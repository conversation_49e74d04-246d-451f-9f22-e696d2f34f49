using Bootis.Compra.Domain.Enumerations;
using Bootis.Organizacional.Domain.AggregatesModel.UsuarioAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate;

public class PedidoCompraHistorico : Entity, IAggregateRoot, ITenant
{
    protected PedidoCompraHistorico()
    {
    }

    public PedidoCompraHistorico(Usuario usuario, StatusCompra statusAlterado, bool estorno) : this()
    {
        Usuario = usuario;
        StatusAlterado = statusAlterado;
        Data = DateTime.UtcNow;
        Estorno = estorno;

        if (statusAlterado == StatusCompra.Liberado)
            PedidoCompraHistoricoAprovacao = new PedidoCompraHistoricoAprovacao(this);
    }

    public Guid UsuarioId { get; private set; }
    public DateTime Data { get; private set; }
    public StatusCompra StatusAlterado { get; private set; }
    public bool Estorno { get; private set; }
    public Guid PedidoCompraId { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void CalcularAprovacaoItem(PedidoCompra pedidoCompra)
    {
        PedidoCompraHistoricoAprovacao.CalcularAprovacaoItem(pedidoCompra);
    }

    #region Navigation properties

    public virtual Usuario Usuario { get; set; }
    public virtual PedidoCompra PedidoCompra { get; set; }
    public virtual PedidoCompraHistoricoAprovacao PedidoCompraHistoricoAprovacao { get; set; }

    #endregion
}