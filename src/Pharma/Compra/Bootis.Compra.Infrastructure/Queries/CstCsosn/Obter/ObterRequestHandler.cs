using System.Data;
using Bootis.Compra.Application.Requests.CstCsosn.Obter;
using Bootis.Compra.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Compra.Infrastructure.Queries.CstCsosn.Obter;

public class ObterRequestHandler(
    IDbConnection connection) : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT 
                               id,
                               codigo,
                               descricao
                           FROM 
                               csts_csosn
                           WHERE 
                               id = @id
                           """;

        var result = await connection.QueryAsync<ObterResponse>(sql, new
        {
            request.Id
        });

        if (result.Any())
            return result.FirstOrDefault();

        var message = Localizer.Instance.GetMessage_Cfop_IdNaoEncontrado(request.Id);

        throw new DomainException(message);
    }
}