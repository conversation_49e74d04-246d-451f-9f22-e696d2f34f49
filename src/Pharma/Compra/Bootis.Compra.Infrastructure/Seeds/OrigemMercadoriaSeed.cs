using Bootis.Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;
using UUIDNext;

namespace Bootis.Compra.Infrastructure.Seeds;

public class OrigemMercadoriaSeed : ISeed
{
    public int Order => 1;

    public void Seed(DbContext dbContext)
    {
        dbContext.Database.ExecuteSqlRaw($"""
                                          INSERT INTO origem_mercadorias (id, descricao, codigo) 
                                          VALUES
                                              ('{Uuid.NewSequential()}', 'Nacional, exceto as indicadas nos códigos 3 a 5', 0),
                                              ('{Uuid.NewSequential()}', 'Estrangeira - Importação direta, exceto a indicada no código 6', 1),
                                              ('{Uuid.NewSequential()}', 'Estrangeira - Adquirida no mercado interno, exceto a indicada no código 7', 2),
                                              ('{Uuid.NewSequential()}', 'Nacional, mercadoria ou bem com Conteúdo de Importação superior a 40%', 3),
                                              ('{Uuid.NewSequential()}', 'Nacional, cuja produção tenha sido feita em conformidade com os processos produtivos básicos de que tratam o Decreto-Lei nº 288/67 e as Leis nºs 8.248/91, 8.387/91, 10.176/01 e 11.484/07', 4),
                                              ('{Uuid.NewSequential()}', 'Nacional, mercadoria ou bem com Conteúdo de Importação inferior ou igual a 40%', 5),
                                              ('{Uuid.NewSequential()}', 'Estrangeira - Importação direta, sem similar nacional, constante em lista de Resolução CAMEX', 6),
                                              ('{Uuid.NewSequential()}', 'Estrangeira - Adquirida no mercado interno, sem similar nacional, constante em lista de Resolução CAMEX', 7);
                                          """);
    }
}