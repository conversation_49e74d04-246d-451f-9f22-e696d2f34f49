using Bootis.Compra.Application.Requests.PedidoCompra.Atualizar;
using Bootis.Compra.Domain.Dtos.PedidoCompra;
using Bootis.Shared;
using Bootis.Shared.Common.Extensions;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Compra.Application.Validations.PedidoCompra;

public class AtualizarRequestValidator : AbstractValidator<AtualizarRequest>
{
    public AtualizarRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.FornecedorId)
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.FornecedorId)));

        RuleFor(c => c.Tipo<PERSON>es<PERSON>to)
            .IsInEnum();

        RuleFor(c => c.PrevisaoEntrega)
            .Must(SeAnteriorOuPosteriorDataAtual)
            .WithMessage("A data de entrega não pode ser anterior ao dia atual");

        RuleFor(c => c.Frete)
            .NotNull()
            .WithMessage(l => localizer.GetMessage_Validation_CampoRequerido(nameof(l.Frete)));

        RuleForEach(c => c.PedidoCompraItens)
            .SetValidator(new AtualizarPedidoCompraItemRequestValidator(localizer));
    }

    private bool SeAnteriorOuPosteriorDataAtual(DateOnly? data)
    {
        if (data == null)
            return true;
        return data.Value >= DateTime.UtcNow.Date.ToDateOnly();
    }
}

public class AtualizarPedidoCompraItemRequestValidator : AbstractValidator<PedidoCompraItemDto>
{
    public AtualizarPedidoCompraItemRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.ProdutoId)
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.ProdutoId)));

        RuleFor(c => c.Quantidade)
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.Quantidade)));

        RuleFor(c => c.UnidadeMedidaId)
            .IsInEnum();

        RuleFor(c => c.TipoDesconto)
            .IsInEnum();
    }
}