using Bootis.Compra.Domain.Dtos.NotaFiscalEntrada;
using MediatR;

namespace Bootis.Compra.Application.Requests.NotaFiscalEntrada.Atualizar;

public class AtualizarLoteRascunhoRequest : IRequest
{
    public Guid NotaFiscalEntradaId { get; set; }
    public ICollection<LoteRascunhoRequest> Lotes { get; set; }
}

public class LoteRascunhoRequest : NotaFiscalEntradaLoteRascunhoDto
{
    public Guid? NotaFiscalEntradaLoteId { get; set; }
}