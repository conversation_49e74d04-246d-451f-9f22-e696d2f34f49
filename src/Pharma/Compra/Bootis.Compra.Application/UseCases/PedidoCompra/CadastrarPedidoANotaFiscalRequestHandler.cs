using Bootis.Compra.Application.Requests.PedidoCompra.Atualizar;
using Bootis.Compra.Application.Requests.PedidoCompra.Cadastrar;
using Bootis.Compra.Domain.AggregatesModel.PedidoCompraAggregate;
using MediatR;

namespace Bootis.Compra.Application.UseCases.PedidoCompra;

public class CadastrarPedidoANotaFiscalRequestHandler(
    IPedidoCompraRepository pedidoRepository,
    IMediator mediator)
    : IRequestHandler<CadastrarPedidoANotaFiscalRequest, bool>
{
    public async Task<bool> Handle(CadastrarPedidoANotaFiscalRequest request, CancellationToken cancellationToken)
    {
        var pedidoItensBaixar = new List<Guid>();

        foreach (var pedido in request.NotaFiscalEntradaPedido)
        {
            var pedidoCompra = await pedidoRepository.GetByIdAsync(pedido.PedidoCompraId).ConfigureAwait(false);

            var itensMapeados = from pedidoItem in pedidoCompra.Itens
                join notaItem in request.NotaFiscalEntrada.NotaFiscalEntradaItem
                    on pedidoItem.ProdutoId equals notaItem.Produto.Id
                select new PedidoCompraItemNotaFiscal
                {
                    PedidoCompraItemId = pedidoItem.Id,
                    NotaFiscalEntradaItemId = notaItem.Id,
                    Quantidade = notaItem.QuantidadeComprada.GetValueOrDefault(),
                    UnidadeMedidaId = notaItem.UnidadeId.GetValueOrDefault()
                };

            foreach (var item in itensMapeados)
            {
                PedidoCompraItemNotaFiscal pedidoCompraItemNotaFiscal;

                pedidoCompraItemNotaFiscal = new PedidoCompraItemNotaFiscal(item.PedidoCompraItemId,
                    item.NotaFiscalEntradaItemId,
                    item.UnidadeMedidaId,
                    item.Quantidade);
                pedidoItensBaixar.Add(item.PedidoCompraItemId);

                pedidoRepository.Add(pedidoCompraItemNotaFiscal);
            }

            var baixarRequest = new BaixarRequest
            {
                PedidoCompraId = pedidoCompra.Id,
                PedidoCompraItensId = pedidoItensBaixar
            };

            await BaixarPedidos(baixarRequest);
        }

        return true;
    }

    private async Task BaixarPedidos(BaixarRequest command)
    {
        await mediator.Send(command).ConfigureAwait(false);
    }
}