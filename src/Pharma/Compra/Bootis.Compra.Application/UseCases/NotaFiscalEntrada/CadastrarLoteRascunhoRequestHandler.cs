using Bootis.Compra.Application.Extensions;
using Bootis.Compra.Application.Requests.NotaFiscalEntrada.Cadastrar;
using Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate;
using Bootis.Estoque.Domain.AggregatesModel.LocalEstoqueAggregate;
using Bootis.Localidade.Domain.AggregatesModel.PaisAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Compra.Application.UseCases.NotaFiscalEntrada;

public class CadastrarLoteRascunhoRequestHandler(IUnitOfWork unitOfWork) : IRequestHandler<CadastrarLoteRascunhoRequest>
{
    private readonly ILocalEstoqueRepository _localEstoqueRepository =
        unitOfWork.GetRepository<ILocalEstoqueRepository>();

    private readonly INotaFiscalEntradaRepository _notaFiscalEntradaRepository =
        unitOfWork.GetRepository<INotaFiscalEntradaRepository>();

    private readonly IPaisRepository _paisRepository = unitOfWork.GetRepository<IPaisRepository>();

    public async Task Handle(CadastrarLoteRascunhoRequest request, CancellationToken cancellationToken)
    {
        var notaFiscalEntrada =
            await _notaFiscalEntradaRepository.ObterNotaFiscalEntradaAsync(request.NotaFiscalEntradaId);

        notaFiscalEntrada.ValidarSePodeLancar();

        foreach (var loteRequest in request.Lotes)
        {
            var notaFiscalEntradaItem =
                notaFiscalEntrada.NotaFiscalEntradaItem.Single(c => c.Id == loteRequest.NotaFiscalEntradaItemId);
            var localEstoqueId = loteRequest.LocalEstoqueId;
            var paisId = loteRequest.InformacaoTecnica?.PaisOrigemId;

            notaFiscalEntradaItem.AdicionarNotaFiscalEntradaLoteRascunho(loteRequest, localEstoqueId,
                notaFiscalEntradaItem.Produto, paisId);
        }

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}