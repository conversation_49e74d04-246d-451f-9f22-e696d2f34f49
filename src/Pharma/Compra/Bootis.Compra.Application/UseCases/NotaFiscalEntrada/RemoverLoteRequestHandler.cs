using Bootis.Compra.Application.Extensions;
using Bootis.Compra.Application.Requests.NotaFiscalEntrada.Remover;
using Bootis.Compra.Domain.AggregatesModel.NotaFiscalEntradaAggregate;
using Bootis.Compra.Domain.Enumerations;
using Bootis.Compra.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using MediatR;

namespace Bootis.Compra.Application.UseCases.NotaFiscalEntrada;

public class RemoverLoteRequestHandler(IUnitOfWork unitOfWork) : IRequestHandler<RemoverLoteRequest>
{
    private readonly INotaFiscalEntradaRepository _notaFiscalEntradaRepository =
        unitOfWork.GetRepository<INotaFiscalEntradaRepository>();

    public async Task Handle(RemoverLoteRequest request, CancellationToken cancellationToken)
    {
        var notaFiscalEntrada =
            await _notaFiscalEntradaRepository.ObterNotaFiscalEntradaAsync(request.NotaFiscalEntradaId);

        if (notaFiscalEntrada.Status != StatusNotaFiscal.Pendente)
            throw new ValidationException(nameof(notaFiscalEntrada.Id),
                Localizer.Instance.GetMessage_NotaFiscalEntradaLote_NaoPodeSerRemovido(notaFiscalEntrada.Id));

        var notasFiscaisEntradaLotes =
            notaFiscalEntrada.NotaFiscalEntradaItem.SelectMany(c => c.NotaFiscalEntradaLote).ToList();

        foreach (var notaFiscalEntradaLote in notasFiscaisEntradaLotes)
            _notaFiscalEntradaRepository.Remove(notaFiscalEntradaLote);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}