using Bootis.Pessoa.Domain.AggregatesModel.TipoDocumentoAggregate;
using Bootis.Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;

namespace Bootis.Pessoa.Infrastructure.Repositories;

public class TipoDocumentoRepository(IDbContext context) : Repository<TipoDocumento>(context), ITipoDocumentoRepository
{
    public async Task<TipoDocumento> FindWithoutIncludesAsync(Guid id)
    {
        return await DbSet
            .SingleOrDefaultAsync(c => c.Id == id);
    }
}