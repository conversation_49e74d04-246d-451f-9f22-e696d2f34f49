using Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Pessoa.Infrastructure.EntityConfigurations;

public class PrescritorContatoEntityTypeConfiguration : BaseEntityTypeConfiguration<PrescritorContato>
{
    public override void Configure(EntityTypeBuilder<PrescritorContato> builder)
    {
        builder.ToTable("prescritor_contatos");

        builder
            .HasOne(p => p.Tipo<PERSON>ontato)
            .WithMany()
            .HasForeignKey(p => p.TipoContatoId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(c => c.TipoContatoId)
            .IsRequired();

        builder
            .HasOne(p => p.Prescritor)
            .WithMany(b => b.Contatos)
            .HasForeignKey(p => p.PrescritorId)
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .Property(c => c.PrescritorId)
            .IsRequired();

        builder
            .Property(c => c.Principal)
            .HasDefaultValue(false)
            .IsRequired();

        builder
            .Property(c => c.Identificacao)
            .Documento(TamanhoTexto.Cinquenta)
            .IsRequired();

        builder
            .Property(c => c.Observacao)
            .Observacao(TamanhoTexto.Cem)
            .IsRequired(false);

        base.Configure(builder);
    }
}