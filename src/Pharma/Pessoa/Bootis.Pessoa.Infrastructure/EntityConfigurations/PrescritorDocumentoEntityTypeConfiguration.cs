using Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate;
using Bootis.Shared.Infrastructure.EntityConfigurations;
using Bootis.Shared.Infrastructure.Enums;
using Bootis.Shared.Infrastructure.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Pessoa.Infrastructure.EntityConfigurations;

public class PrescritorDocumentoEntityTypeConfiguration : BaseEntityTypeConfiguration<PrescritorDocumento>
{
    public override void Configure(EntityTypeBuilder<PrescritorDocumento> builder)
    {
        builder.ToTable("prescritor_documentos");

        builder
            .HasOne(p => p.TipoDocumento)
            .WithMany()
            .HasForeignKey(p => p.TipoDocumentoId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(c => c.TipoDocumentoId)
            .IsRequired();

        builder
            .HasOne(p => p.Prescritor)
            .WithMany(b => b.Documentos)
            .HasForeignKey(p => p.PrescritorId)
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .Property(c => c.PrescritorId)
            .IsRequired();

        builder
            .Property(c => c.Identificacao)
            .Documento(TamanhoTexto.Cinquenta)
            .IsRequired();

        builder
            .Property(c => c.Observacao)
            .Observacao(TamanhoTexto.Cem)
            .IsRequired(false);

        base.Configure(builder);
    }
}