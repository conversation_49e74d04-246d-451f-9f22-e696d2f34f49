using System.Data;
using Bootis.Pessoa.Application.Requests.Fornecedor.Validar;
using Bootis.Pessoa.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Pessoa.Infrastructure.Queries.Fornecedor.Validar;

public class ValidarCpfRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ValidarCpfRequest>
{
    public async Task Handle(ValidarCpfRequest request, CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT CASE WHEN EXISTS (SELECT 1 
                                                     FROM fornecedores fo
                                                     WHERE fo.cpf = @cpf
                                                       AND fo.group_tenant_id = @groupTenantId)
                                       THEN 1 ELSE 0 END AS existe_registro;
                           """;

        var result = await connection.QuerySingleOrDefaultAsync<bool>(sql,
            new { cpf = request.Cpf, groupTenantId = userContext.GroupTenantId });

        if (result) throw new DomainException(Localizer.Instance.GetMessage_Fornecedor_CpfExistente(request.Cpf));
    }
}