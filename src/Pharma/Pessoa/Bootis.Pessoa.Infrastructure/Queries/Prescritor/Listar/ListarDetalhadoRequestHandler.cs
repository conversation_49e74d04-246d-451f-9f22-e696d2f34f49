using System.Data;
using Bootis.Pessoa.Application.Requests.Prescritor.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.FilterFields;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Pessoa.Infrastructure.Queries.Prescritor.Listar;

public class
    ListarDetalhadoRequestHandler(
        IUserContext userContext,
        IDbConnection connection)
    : IRequestHandler<ListarDetalhadoRequest, PaginatedResult<ListarDetalhadoResponse>>
{
    public async Task<PaginatedResult<ListarDetalhadoResponse>> Handle(ListarDetalhadoRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT p.nome_completo,
                                  p.id,
                                  p.id,
                                  tr.sigla AS tipo_registro_sigla,
                                  es.abreviacao AS abreviacao_uf_registro,
                                  p.codigo_registro,
                                  p.ativo,
                                  pc.identificacao AS contato_principal,
                                  tc.icon AS tipo_contato_icon,
                                  (SELECT string_agg(especialidade, ',')
                                     FROM (SELECT DISTINCT ep.descricao AS especialidade
                                           FROM prescritores_especialidades pe
                                           JOIN especialidades_prescritor ep ON ep.id = pe.especialidade_id
                                           WHERE pe.prescritor_id = p.id) AS subquery) AS especialidades
                           FROM prescritores p
                           LEFT JOIN prescritor_contatos pc ON pc.prescritor_id = p.id
                              AND pc.principal = true
                           LEFT JOIN estados es ON es.id = p.uf_registro_id
                           LEFT JOIN tipos_contato tc ON tc.id = pc.tipo_contato_id
                           LEFT JOIN tipos_registro tr ON tr.id = p.tipo_registro_id
                           WHERE p.group_tenant_id = @GroupTenantId
                           !@SEARCH_CONDITION@!
                           """;

        var conditionalEspecialidadeId = new ConditionalFilterField<ListarDetalhadoRequest>
        {
            Filter =
                "P.id IN (SELECT pe2.prescritor_id FROM prescritores_especialidades pe2 LEFT JOIN especialidades_prescritor ep2 ON ep2.id = pe2.especialidade_id WHERE ep2.id = @EspecialidadeId)",
            Predicate = filter => filter.EspecialidadeId != null
        };

        var searchNomePrescritor = new StringSearchField
        {
            Field = "P.nome_completo",
            CompareType = StringCompareType.Contains
        };

        var searchRegistroCompleto = new StringSearchField
        {
            Field = "CONCAT(TR.sigla, '/', ES.abreviacao, ' ', P.codigo_registro)",
            CompareType = StringCompareType.Contains
        };

        var searchCodigoRegistro = new StringSearchField
        {
            Field = "P.codigo_registro",
            CompareType = StringCompareType.Contains
        };

        var searchContatoPrescritor = new StringSearchField
        {
            Field = "PC.identificacao",
            CompareType = StringCompareType.Contains
        };

        var searchContatoPrescritorNumerico = new StringSearchField
        {
            Field = "PC.identificacao",
            CompareType = StringCompareType.Contains,
            OnlyNumbers = true,
            RegexFilter = RegexConstants.PhoneNumber
        };

        var result = await PaginatedQueryBuilder<ListarDetalhadoRequest, QueryResult>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchNomePrescritor)
            .AddSearchField(searchRegistroCompleto)
            .AddSearchField(searchCodigoRegistro)
            .AddSearchField(searchContatoPrescritor)
            .AddSearchField(searchContatoPrescritorNumerico)
            .AddFilter(conditionalEspecialidadeId)
            .ExecuteAsync();

        var prescritores = result.Data;

        var queryResult = prescritores.Select(prescritorItem => new ListarDetalhadoResponse
            {
                Id = prescritorItem.Id,
                NomeCompleto = prescritorItem.NomeCompleto,
                TipoRegistroSigla = prescritorItem.TipoRegistroSigla,
                AbreviacaoUfRegistro = prescritorItem.AbreviacaoUfRegistro,
                CodigoRegistro = prescritorItem.CodigoRegistro,
                Ativo = prescritorItem.Ativo,
                ContatoPrincipal = prescritorItem.ContatoPrincipal,
                TipoContatoIcon = prescritorItem.TipoContatoIcon,
                Especialidades = prescritorItem.Especialidades?.Split(',')
            })
            .ToList();

        return new PaginatedResult<ListarDetalhadoResponse>(result.PageIndex, result.PageSize, result.Count,
            queryResult);
    }

    private sealed record QueryResult
    {
        public Guid Id { get; set; }
        public string NomeCompleto { get; set; }
        public string TipoRegistroSigla { get; set; }
        public string AbreviacaoUfRegistro { get; set; }
        public string CodigoRegistro { get; set; }
        public bool Ativo { get; set; }
        public string ContatoPrincipal { get; set; }
        public string TipoContatoIcon { get; set; }
        public string Especialidades { get; set; }
    }
}