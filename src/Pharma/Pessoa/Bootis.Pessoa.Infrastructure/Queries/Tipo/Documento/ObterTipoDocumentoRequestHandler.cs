using System.Data;
using Bootis.Pessoa.Application.Requests.Tipo.Documento.Obter;
using Bootis.Pessoa.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Pessoa.Infrastructure.Queries.Tipo.Documento;

public class ObterRequestHandler(
    IDbConnection connection) : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT td.id,
                                  td.nome
                           FROM tipos_documento td
                           WHERE td.id = @id
                           """;

        var result = await connection.QueryFirstOrDefaultAsync<ObterResponse>(sql, new { request.Id });

        if (result is not null)
            return result;

        throw new DomainException(Localizer.Instance.GetMessage_TipoDocumento_IdNaoEncontrado(request.Id));
    }
}