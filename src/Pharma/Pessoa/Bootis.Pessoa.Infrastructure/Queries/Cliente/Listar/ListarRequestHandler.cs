using System.Data;
using Bootis.Pessoa.Application.Requests.Cliente.Listar;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Query;
using Bootis.Shared.Infrastructure.Query;
using Bootis.Shared.Infrastructure.Query.Enums;
using Bootis.Shared.Infrastructure.Query.SearchFields;
using MediatR;

namespace Bootis.Pessoa.Infrastructure.Queries.Cliente.Listar;

public class ListarRequestHandler(IUserContext userContext, IDbConnection connection)
    : IRequestHandler<ListarRequest, PaginatedResult<ListarResponse>>
{
    public Task<PaginatedResult<ListarResponse>> Handle(ListarRequest request,
        CancellationToken cancellationToken)
    {
        const string sql = """
                           SELECT 
                                  nome,
                                  id,
                                  sequencia_group_tenant AS codigo,
                                  CASE 
                                      WHEN pessoa = 0 THEN 'CPF'
                                      WHEN pessoa = 1 THEN 'CNPJ'
                                  END AS sigla_documento,
                                  cpf,
                                  cnpj
                             FROM clientes
                            WHERE group_tenant_id = @GroupTenantId
                              !@SEARCH_CONDITION@!
                           """;

        var searchNome = new StringSearchField
        {
            Field = "nome",
            CompareType = StringCompareType.Contains
        };

        var searchCodigo = new NumberSearchField
        {
            Field = "sequencia_group_tenant",
            CompareType = NumericCompareType.Contains
        };

        return PaginatedQueryBuilder<ListarRequest, ListarResponse>
            .Create(connection, sql, request, userContext)
            .AddSearchField(searchNome)
            .AddSearchField(searchCodigo)
            .ExecuteAsync();
    }
}