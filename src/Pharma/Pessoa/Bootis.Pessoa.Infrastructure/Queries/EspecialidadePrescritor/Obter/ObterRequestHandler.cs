using System.Data;
using Bootis.Pessoa.Application.Requests.EspecialidadePrescritor.Obter;
using Bootis.Pessoa.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Pessoa.Infrastructure.Queries.EspecialidadePrescritor.Obter;

public class ObterRequestHandler(
    IUserContext userContext,
    IDbConnection connection)
    : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        var sql = """
                  SELECT esp.id, 
                         esp.descricao,
                         esp.ativo
                    FROM especialidades_prescritor esp
                   WHERE esp.id = @id 
                     AND esp.group_tenant_id = @groupTenantId;
                  """;

        var result = await connection
            .QueryFirstOrDefaultAsync<ObterResponse>(sql,
                new { request.Id, userContext.GroupTenantId });

        if (result is null)
            throw new DomainException(
                Localizer.Instance.GetMessage_EspecialidadePrescritor_GuidNaoEncontrado(request.Id));

        return result;
    }
}