using Bootis.Pessoa.Application.Extensions;
using Bootis.Pessoa.Application.Requests.EspecialidadePrescritor.Atualizar;
using Bootis.Pessoa.Domain.AggregatesModel.EspecialidadePrescritorAggregate;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common.Extensions;
using MediatR;

namespace Bootis.Pessoa.Application.UseCases.EspecialidadePrescritor;

public class AtualizarRequestHandler(
    IUnitOfWork unitOfWork,
    IUserContext userContext,
    IEspecialidadePrescritorRepository especialidadePrescritorRepository)
    : IRequestHandler<AtualizarRequest>
{
    public async Task Handle(AtualizarRequest request, CancellationToken cancellationToken)
    {
        await especialidadePrescritorRepository.ValidarEspecialidadePrescritorAsync(userContext, request.Descricao);

        var especialidadePrescritor = await especialidadePrescritorRepository
            .ObterEspecialidadePrescritorAsync(request.EspecialidadePrescritorId).ConfigureAwait(false);

        especialidadePrescritor.ExecuteIfValid(l => l.Descricao != request.Descricao,
            l => l.AtualizarDescricao(request.Descricao));

        especialidadePrescritorRepository.Update(especialidadePrescritor);

        await unitOfWork.SaveChangesAsync(cancellationToken)
            .ConfigureAwait(false);
    }
}