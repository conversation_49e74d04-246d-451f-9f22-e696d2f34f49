using Bootis.Pessoa.Application.Extensions;
using Bootis.Pessoa.Application.Requests.Fornecedor.Atualizar;
using Bootis.Pessoa.Domain.AggregatesModel.FornecedorAggregate;
using Bootis.Shared.Application.Interfaces;
using MediatR;

namespace Bootis.Pessoa.Application.UseCases.Fornecedor;

public class AtualizarEnderecoPrincipalRequestHandler(
    IUnitOfWork unitOfWork,
    IFornecedorRepository fornecedorRepository)
    : IRequestHandler<AtualizarEnderecoPrincipalRequest>
{
    public async Task Handle(AtualizarEnderecoPrincipalRequest request, CancellationToken cancellationToken)
    {
        var fornecedorEndereco =
            await fornecedorRepository.ObterFornecedorEnderecoAsync(request.FornecedorEnderecoId);

        if (request.Principal)
        {
            var outrosEnderecos =
                await fornecedorRepository.ObterEnderecosPorIdEhEnderecoIdAsync(
                    fornecedorEndereco.FornecedorId, request.FornecedorEnderecoId);

            if (outrosEnderecos != null)
                foreach (var endereco in outrosEnderecos)
                {
                    endereco.AtualizarPrincipal(false);
                    fornecedorRepository.Update(endereco);
                }
        }

        fornecedorEndereco.AtualizarPrincipal(request.Principal);

        fornecedorRepository.Update(fornecedorEndereco);

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}