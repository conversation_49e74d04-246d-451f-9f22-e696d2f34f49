using Bootis.Pessoa.Application.Requests.Prescritor.Cadastrar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Pessoa.Application.Validations.Prescritor;

public class CadastrarPrescritorRequestValidator : AbstractValidator<CadastrarRequest>
{
    public CadastrarPrescritorRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.NomeCompleto)
            .NotNull()
            .NotEmpty()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.NomeCompleto)));

        RuleFor(c => c.TipoRegistroId)
            .NotNull()
            .NotEmpty()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.TipoRegistroId)));

        RuleFor(c => c.CodigoRegistro)
            .NotNull()
            .NotEmpty()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.CodigoRegistro)));

        RuleFor(c => c.UfRegistroId)
            .NotNull()
            .NotEmpty()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.UfRegistroId)));

        RuleFor(c => c.DescontoFormulas)
            .InclusiveBetween(0, 100)
            .WithMessage(f => localizer.GetMessage_Validation_ValorInvalido(nameof(f.DescontoFormulas), 0, 100));

        RuleFor(c => c.DescontoProdutosAcabados)
            .InclusiveBetween(0, 100)
            .WithMessage(f =>
                localizer.GetMessage_Validation_ValorInvalido(nameof(f.DescontoProdutosAcabados), 0, 100));

        RuleFor(c => c.Observacao)
            .MaximumLength(1000)
            .WithMessage(f => localizer.GetMessage_Validation_TamanhoMaximo(f.Observacao, 1000));
    }
}