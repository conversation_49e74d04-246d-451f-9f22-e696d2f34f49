using Bootis.Pessoa.Application.Requests.Fornecedor.Cadastrar;
using Bootis.Shared;
using FluentValidation;
using Microsoft.Extensions.Localization;

namespace Bootis.Pessoa.Application.Validations.Fornecedor;

public class CadastrarEnderecoFornecedorRequestValidator : AbstractValidator<CadastrarEnderecoRequest>
{
    public CadastrarEnderecoFornecedorRequestValidator(IStringLocalizer localizer)
    {
        RuleFor(c => c.Descricao)
            .NotEmpty()
            .NotNull()
            .WithMessage(f => localizer.GetMessage_Validation_CampoRequerido(nameof(f.Des<PERSON>ricao)));
    }
}