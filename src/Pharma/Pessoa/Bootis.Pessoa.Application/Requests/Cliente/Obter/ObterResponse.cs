using System.ComponentModel;
using Bootis.Pessoa.Domain.Enumerations;

namespace Bootis.Pessoa.Application.Requests.Cliente.Obter;

public class ObterResponse
{
    public Guid Id { get; set; }
    public string Nome { get; set; }
    public int Codigo { get; set; }
    public TipoPessoa TipoPessoa { get; set; }

    public string TipoPessoaDescricao
    {
        get
        {
            var descriptionAttribute = typeof(TipoPessoa)
                    .GetField(TipoPessoa.ToString())
                    .GetCustomAttributes(typeof(DescriptionAttribute), false)
                as DescriptionAttribute[];

            return descriptionAttribute != null && descriptionAttribute.Length > 0
                ? descriptionAttribute[0].Description
                : TipoPessoa.ToString();
        }
    }

    public bool Ativo { get; set; }
    public string Cnpj { get; set; }
    public string Cpf { get; set; }
    public string RazaoSocial { get; set; }
    public DateTime? DataNascimento { get; set; }
    public decimal? DescontoProdutosAcabados { get; set; }
    public decimal? DescontoFormulas { get; set; }
    public string Observacao { get; set; }
    public IEnumerable<ClienteContatoResponse> ClienteContatos { get; set; }
    public IEnumerable<ClienteDocumentoResponse> ClienteDocumentos { get; set; }
    public IEnumerable<ClienteEnderecoResponse> ClienteEnderecos { get; set; }

    public class ClienteContatoResponse
    {
        public Guid? ContatoId { get; set; }
        public Guid TipoContatoId { get; set; }
        public string DescricaoContato { get; set; }
        public string TipoContatoIcon { get; set; }
        public string ContatoIdentificacao { get; set; }
        public bool ContatoPrincipal { get; set; }
        public string ContatoObservacao { get; set; }
    }

    public class ClienteDocumentoResponse
    {
        public Guid? DocumentoId { get; set; }
        public Guid TipoDocumentoId { get; set; }
        public string NomeDocumento { get; set; }
        public string DocumentoIdentificacao { get; set; }
        public string DocumentoObservacao { get; set; }
    }

    public class ClienteEnderecoResponse
    {
        public Guid? EnderecoId { get; set; }
        public Guid? PaisId { get; set; }
        public string PaisDescricao { get; set; }
        public Guid? EstadoId { get; set; }
        public string EstadoDescricao { get; set; }
        public Guid? CidadeId { get; set; }
        public string CidadeDescricao { get; set; }
        public string Bairro { get; set; }
        public string Cep { get; set; }
        public string Logradouro { get; set; }
        public string Numero { get; set; }
        public string Complemento { get; set; }
        public bool EnderecoPrincipal { get; set; }
        public string EnderecoDescricao { get; set; }
    }
}