using Bootis.Pessoa.Application.Requests.EspecialidadePrescritor.Remover;
using Bootis.Pessoa.Domain.AggregatesModel.EspecialidadePrescritorAggregate;
using Bootis.Pessoa.Resources;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Pessoa.Application.Extensions;

public static class EspecialidadePrescritorRepositoryExtension
{
    public static async Task<EspecialidadePrescritor> ObterEspecialidadePrescritorAsync(
        this IEspecialidadePrescritorRepository repository,
        Guid id)
    {
        var especialidadePrescritor = await repository.ObterPorIdAsync(id).ConfigureAwait(false);

        if (especialidadePrescritor == null)
            throw new ValidationException(nameof(id),
                Localizer.Instance.GetMessage_EspecialidadePrescritor_GuidNaoEncontrado(id));

        return especialidadePrescritor;
    }

    public static async Task<List<EspecialidadePrescritor>> ObterEspecialidadesPrescritorAsync(
        this IEspecialidadePrescritorRepository repository,
        IEnumerable<Guid> ids)
    {
        var especialidadesPrescritor = await repository.ObterPorIdsAsync(ids);

        var idsInvalidos = ids
            .Distinct()
            .Where(id => !especialidadesPrescritor.Select(c => c.Id).Contains(id))
            .Select(Localizer.Instance.GetMessage_EspecialidadePrescritor_GuidNaoEncontrado);

        if (idsInvalidos.Any())
            throw new ValidationException(nameof(idsInvalidos),
                idsInvalidos);

        return especialidadesPrescritor;
    }

    public static async Task ValidarEspecialidadePrescritorAsync(this IEspecialidadePrescritorRepository repository,
        IUserContext userContext,
        string descricao)
    {
        var especialidadePrescritor = await repository.ObterPorDescricaoAsync(descricao);

        if (especialidadePrescritor != null)
            throw new ValidationException(nameof(descricao),
                Localizer.Instance.GetMessage_EspecialidadePrescritor_DescricaoExistente(descricao,
                    userContext.GroupTenantId));
    }

    public static async Task VerificarDependenciasAsync(this IEspecialidadePrescritorRepository repository,
        List<EspecialidadePrescritor> especialidadesPrescritor)
    {
        var removeResponses = new List<RemoverResponse>();

        foreach (var especialidadePrescritor in especialidadesPrescritor)
            if (await repository.VerificaDependenciaEspecialidadePrescritorAsync(especialidadePrescritor.Id) >
                0)
                removeResponses.Add(new RemoverResponse
                {
                    EspecialidadePrescritorId = especialidadePrescritor.Id,
                    EspecialidadePrescritorDescricao = especialidadePrescritor.Descricao
                });

        if (removeResponses.Count > 0)
            throw new DomainException(nameof(GlobalErrorCode), (int)GlobalErrorCode.Global_Remover,
                removeResponses.ToArray());
    }
}