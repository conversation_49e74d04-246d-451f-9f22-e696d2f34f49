using Asp.Versioning;
using Bootis.Pessoa.Application.Requests.Tipo.Documento.Listar;
using Bootis.Pessoa.Application.Requests.Tipo.Documento.Obter;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Pessoa.Api.Controllers.V1;

[Authorize]
[ApiController]
[ApiVersion(1)]
[ApiExplorerSettings(GroupName = "Pessoa")]
[Route("pessoa/v{version:apiVersion}/[controller]")]
public class TipoDocumentoController(IMediator mediator) : ControllerBase
{
    [HttpGet]
    [Route("Obter/{id:Guid}")]
    [ProducesResponseType(typeof(ObterResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Obter(Guid id)
    {
        var request = new ObterRequest { Id = id };

        var result = await mediator.Send(request);

        return Ok(result);
    }


    [HttpGet]
    [Route("Listar")]
    [ProducesResponseType(typeof(ListarResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> ListarTipoPessoa([FromQuery] ListarRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }
}