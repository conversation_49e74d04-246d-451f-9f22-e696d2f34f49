using Bootis.Pessoa.Domain.AggregatesModel.TipoContatoAggregate;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Pessoa.Domain.AggregatesModel.PrescritorAggregate;

public class PrescritorContato : Contato, IAggregateRoot, ITenant
{
    public PrescritorContato()
    {
    }

    public PrescritorContato(Prescritor prescritor,
        string identificacao) : this()
    {
        Prescritor = prescritor;
        Identificacao = identificacao;
    }

    public PrescritorContato(Guid prescritorId,
        Guid tipoContatoId,
        string identificacao,
        bool principal,
        string observacao) : this()
    {
        PrescritorId = prescritorId;
        TipoContatoId = tipoContatoId;
        Identificacao = identificacao;
        Principal = principal;
        Observacao = observacao;
    }

    public Guid PrescritorId { get; private set; }
    public virtual Prescritor Prescritor { get; private set; }
    public Guid TenantId { get; set; }
    public Guid GroupTenantId { get; set; }

    public void Atualizar(Guid prescritorId, Guid tipoContatoId,
        string identificacao, bool principal, string observacao)
    {
        PrescritorId = prescritorId;
        TipoContatoId = tipoContatoId;
        Observacao = observacao;
        Identificacao = identificacao;
        Principal = principal;
    }

    public void AtualizarPrincipal(bool principal)
    {
        Principal = principal;
    }
}