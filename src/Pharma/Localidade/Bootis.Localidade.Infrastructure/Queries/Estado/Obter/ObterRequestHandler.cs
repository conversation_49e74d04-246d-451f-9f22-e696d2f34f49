using System.Data;
using System.Text;
using Bootis.Localidade.Application.Requests.Estado.Obter;
using Bootis.Localidade.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using Dapper;
using MediatR;

namespace Bootis.Localidade.Infrastructure.Queries.Estado.Obter;

public class ObterRequestHandler(
    IDbConnection connection) : IRequestHandler<ObterRequest, ObterResponse>
{
    public async Task<ObterResponse> Handle(ObterRequest request, CancellationToken cancellationToken)
    {
        var sql = new StringBuilder($"""
                                     SELECT es.id,
                                            es.descricao,
                                            es.abreviacao
                                       FROM estados es
                                      WHERE es.id = '{request.Id}'
                                     """);

        var estado = await connection.QueryAsync<ObterResponse>(sql.ToString());

        if (estado == null || !estado.Any())
            throw new DomainException(Localizer.Instance.GetMessage_Estado_GuidNaoEncontrado(request.Id));

        return estado.FirstOrDefault();
    }
}