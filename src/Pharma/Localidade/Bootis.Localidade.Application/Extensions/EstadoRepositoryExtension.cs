using Bootis.Localidade.Domain.AggregatesModel.EstadoAggregate;
using Bootis.Localidade.Resources;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;

namespace Bootis.Localidade.Application.Extensions;

public static class EstadoRepositoryExtension
{
    public static async Task<Estado> ObterEstadoAsync(this IEstadoRepository repository, Guid? estadoId)
    {
        if (estadoId == null)
            return null;

        var estado = await repository.ObterPorIdAsync(estadoId.Value).ConfigureAwait(false);

        if (estado == null)
            throw new ValidationException(nameof(estadoId),
                Localizer.Instance.GetMessage_Estado_GuidNaoEncontrado(estadoId.Value));

        return estado;
    }
}