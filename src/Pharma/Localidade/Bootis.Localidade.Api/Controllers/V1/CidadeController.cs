using Asp.Versioning;
using Bootis.Localidade.Application.Requests.Cidade.Listar;
using Bootis.Localidade.Application.Requests.Cidade.Obter;
using Bootis.Shared.Common.Query;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Bootis.Localidade.Api.Controllers.V1;

[Authorize]
[ApiController]
[ApiVersion(1)]
[ApiExplorerSettings(GroupName = "Localidade")]
[Route("localidade/v{version:apiVersion}/[controller]")]
public class CidadeController(IMediator mediator) : ControllerBase
{
    [HttpGet]
    [Route("Listar")]
    [ProducesResponseType(typeof(PaginatedResult<ListarResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Listar([FromQuery] ListarRequest request)
    {
        var result = await mediator.Send(request);

        return Ok(result);
    }

    [HttpGet]
    [Route("{id:Guid}")]
    [ProducesResponseType(typeof(ObterResponse), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(string), StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Obter([FromRoute] Guid id)
    {
        var request = new ObterRequest { Id = id };

        var result = await mediator.Send(request);

        return Ok(result);
    }
}