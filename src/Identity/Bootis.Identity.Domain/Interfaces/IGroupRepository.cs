using Bootis.Identity.Domain.Entities;
using Bootis.Shared.Domain.SeedWork;

namespace Bootis.Identity.Domain.Interfaces;

public interface IGroupRepository : IRepository<Group>
{
    #region Basic Group Operations

    Task<Group> GetGroupById(Guid id, CancellationToken cancellationToken);
    Task<Group> GetGroupByIdWithPermissions(Guid id, CancellationToken cancellationToken);
    Task AddAsync(Group group, CancellationToken cancellationToken);
    Task UpdateAsync(Group group, CancellationToken cancellationToken);
    Task RemoveAsync(Group group, CancellationToken cancellationToken);

    #endregion

    #region User-Group Management

    Task ChangeGroups(User user, HashSet<Guid> groupIds, CancellationToken cancellationToken);
    Task<List<Group>> GetGroups(HashSet<Guid> groupIds, CancellationToken cancellationToken);

    #endregion

    #region Optimized Data Access (Cache-Friendly)

    Task<GroupBasicData> GetGroupBasicDataAsync(Guid groupId, CancellationToken cancellationToken = default);
    Task<IEnumerable<int>> GetGroupPermissionsAsync(Guid groupId, CancellationToken cancellationToken = default);
    Task<IEnumerable<int>> GetGroupUserIdsAsync(Guid groupId, CancellationToken cancellationToken = default);
    Task<GroupCompleteData> GetGroupCompleteDataAsync(Guid groupId, CancellationToken cancellationToken = default);

    #endregion
}

#region DTOs for Optimized Access

public record GroupBasicData
{
    public Guid Id { get; init; }
    public bool IsActive { get; init; }
}

public record GroupCompleteData
{
    public bool IsActive { get; init; }
    public IEnumerable<int> Permissions { get; init; } = [];
    public IEnumerable<int> UserIds { get; init; } = [];
}

#endregion