<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>disable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Pharma\Organizacional\Bootis.Organizacional.Contracts\Bootis.Organizacional.Contracts.csproj"/>
        <ProjectReference Include="..\..\Shared\Bootis.Shared.Infrastructure\Bootis.Shared.Infrastructure.csproj"/>
        <ProjectReference Include="..\Bootis.Identity.Application\Bootis.Identity.Application.csproj"/>
        <ProjectReference Include="..\Bootis.Identity.Domain\Bootis.Identity.Domain.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Update="Microsoft.CodeAnalysis.BannedApiAnalyzers" Version="4.14.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

</Project>
