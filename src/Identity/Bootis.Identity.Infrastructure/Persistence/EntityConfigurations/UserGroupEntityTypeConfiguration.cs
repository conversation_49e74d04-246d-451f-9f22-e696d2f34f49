using Bootis.Identity.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Identity.Infrastructure.Persistence.EntityConfigurations;

public class UserGroupEntityTypeConfiguration : IEntityTypeConfiguration<UserGroup>
{
    public void Configure(EntityTypeBuilder<UserGroup> builder)
    {
        builder.ToTable("user_groups");

        builder.HasKey(ug => new { ug.UserId, ug.GroupId });

        builder.Property(ug => ug.UserId)
            .IsRequired();

        builder.Property(ug => ug.GroupId)
            .IsRequired();

        builder.HasOne(ug => ug.User)
            .WithMany(u => u.UserGroups)
            .HasForeignKey(ug => ug.UserId)
            .HasPrincipalKey(u => u.Id)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(ug => ug.Group)
            .WithMany(g => g.UserGroups)
            .HasForeignKey(ug => ug.GroupId)
            .HasPrincipalKey(g => g.Id)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasIndex(ug => ug.UserId);
        builder.HasIndex(ug => ug.GroupId);
    }
}