using Bootis.Identity.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bootis.Identity.Infrastructure.Persistence;

public static class EntityConfigurationHelper
{
    public static Action<OwnedNavigationBuilder<T, Code>> GetCodeBuildAction<T>(string tableName) where T : class
    {
        return builder =>
        {
            builder.WithOwner();

            builder.Property(c => c.Value)
                .HasColumnName("Code")
                .HasMaxLength(50)
                .IsRequired();

            builder.HasIndex(c => c.Value)
                .IsUnique(false);

            builder.Property(c => c.ExpiresAt)
                .IsRequired();

            builder.ToTable(tableName);
        };
    }
}