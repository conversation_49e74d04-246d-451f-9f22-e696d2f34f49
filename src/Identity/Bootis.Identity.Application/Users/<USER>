using Bootis.Identity.Domain.Interfaces;
using Bootis.Shared;
using Bootis.Shared.Application.Interfaces;
using Bootis.Shared.Common;
using Bootis.Shared.Common.Exceptions;
using MediatR;

namespace Bootis.Identity.Application.Users;

public record struct ForgetPasswordRequest(string Email) : IRequest;

public class ForgetPasswordRequestHandler(IUnitOfWork unitOfWork)
    : IRequestHandler<ForgetPasswordRequest>
{
    public async Task Handle(ForgetPasswordRequest request, CancellationToken cancellationToken)
    {
        var userRepository = unitOfWork.GetRepository<IUserRepository>();
        var user = await userRepository.GetByEmail(request.Email, cancellationToken);

        if (user is null)
            throw new ValidationException(nameof(request.Email),
                Localizer.Instance.GetMessage_Usuario_Nao_Encontrado());

        user.ForgetPassword();

        await unitOfWork.SaveChangesAsync(cancellationToken);
    }
}