using System.Collections.Immutable;
using Bootis.Identity.Application.Authentication.Services;
using Bootis.Shared.Common.ValueObjects.Auth;
using MediatR;
using Microsoft.Extensions.Logging;
using OneOf;

namespace Bootis.Identity.Application.Authentication;

public record AuthorizeUserRequest(Guid SessionId, Guid UserId, ImmutableHashSet<int> RequiredRoles)
    : AuthorizeToken(SessionId, RequiredRoles), IRequest<OneOf<AuthorizeSuccess, AuthorizeFailure>>;

public class AuthorizeTokenHandler(
    IAuthorizationService authorizationService,
    ILogger<AuthorizeTokenHandler> logger)
    : IRequestHandler<AuthorizeUserRequest, OneOf<AuthorizeSuccess, AuthorizeFailure>>
{
    public async Task<OneOf<AuthorizeSuccess, AuthorizeFailure>> Handle(
        AuthorizeUserRequest request,
        CancellationToken cancellationToken)
    {
        if (request.SessionId == Guid.Empty || request.UserId == Guid.Empty)
        {
            logger.LogWarning("Token authorization failed: Empty sessionId/userid provided");
            return CreateFailure(AuthenticationError.InvalidSession);
        }

        try
        {
            return await authorizationService.AuthorizeAsync(
                request.UserId,
                request.SessionId,
                request.RequiredRoles,
                cancellationToken);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error during token authorization");
            return CreateFailure(AuthenticationError.InvalidSession);
        }
    }

    private static AuthorizeFailure CreateFailure(AuthenticationError error, bool isActive = false)
    {
        return new AuthorizeFailure(error) { IsActive = isActive };
    }
}