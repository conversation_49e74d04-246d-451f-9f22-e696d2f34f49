using System.Net.Http.Json;
using Bootis.CEP.Contracts.Responses;

namespace Bootis.CEP.Client;

public interface ICepClient
{
    Task<CepResponse> ObterCepAsync(string cep, CancellationToken cancellationToken = default);
}

public class CepClient(HttpClient httpClient) : ICepClient
{
    public async Task<CepResponse> ObterCepAsync(string cep, CancellationToken cancellationToken = default)
    {
        var response = await httpClient.GetAsync($"v1/Cep/{cep}", cancellationToken);

        if (!response.IsSuccessStatusCode) return null;

        if (response.Content.Headers.ContentLength == 0) return null;

        return await response.Content.ReadFromJsonAsync<CepResponse>(cancellationToken);
    }
}