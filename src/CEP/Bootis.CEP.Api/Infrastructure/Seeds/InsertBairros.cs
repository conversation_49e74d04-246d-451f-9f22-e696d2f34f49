using Bootis.Shared.Infrastructure;
using Microsoft.EntityFrameworkCore;
using Npgsql;

namespace Bootis.CEP.Api.Infrastructure.Seeds;

public class InsertBairros : ISeed
{
    public int Order => 4;

    public void Seed(DbContext dbContext)
    {
        var fullFileName =
            Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Infrastructure", "Seeds", "Data", "Bairros.csv");

        var npgsqlConnection = dbContext.Database.GetDbConnection() as NpgsqlConnection;

        using var writer =
            npgsqlConnection!.BeginTextImport(
                "COPY bairros (cidade_id, id, nome) FROM STDIN (FORMAT csv, HEADER false, DELIMITER ';')");
        using var reader = new StreamReader(fullFileName);
        while (reader.ReadLine() is { } line)
            writer.WriteLine(line);
    }
}