using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bootis.CEP.Api.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddIndexOutboxMessages : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "ix_outbox_messages_processed",
                table: "outbox_messages",
                column: "processed");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "ix_outbox_messages_processed",
                table: "outbox_messages");
        }
    }
}
